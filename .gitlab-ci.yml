include:
  - template: 'Workflows/MergeRequest-Pipelines.gitlab-ci.yml'
  - local: .gitlab/ci_templates/push-to-acr.yaml
    inputs:
      image-name: 'reportprocessor'
      image-placeholder: 'reportprocessor:latest'

  - local: .gitlab/ci_templates/s2sauth-test-setup.yaml

  - project: pockethealth/gitlab-templates
    ref: main
    file: jobs/push-to-acr.yaml
    inputs:
      image-name: reportprocessorbase
      image-target: reportprocessorbase
      image-placeholder: unused # this image is not used with kustomization.yaml
      dockerfile-path: baseImage/Dockerfile
      stage: baseimage
    rules:
      - changes:
          paths:
            - baseImage/Dockerfile

  - local: .gitlab/ci_templates/retag-base-image.yaml
    rules:
      - changes:
          paths:
            - baseImage/Dockerfile

stages:
  - baseimage
  - preimage
  - upload
  - qa
  - prod

build_dist:
  tags:
    - k8s-infra-dm
  stage: preimage
  image: phcrcacentral0.azurecr.io/golang:1.24-bullseye
  before_script:
    # --- j<PERSON>rog xray scan ---
    # Download JFrog CLI
    -  curl -fL https://install-cli.jfrog.io | sh
    # configure artifactory server
    - jf config add ph-jfrog --url="https://phtest.jfrog.io/" --access-token=$JFROG_TOKEN
    - jf config show
  script:
    # for gitlab.com/pockethealth golang deps
    - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com".insteadOf https://gitlab.com
    # gosec pkg
    - "curl -sfL https://raw.githubusercontent.com/securego/gosec/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v2.22.3"
    # build
    - PATH=build/bin:$PATH make build dist
    - echo -n "" > dist/build_info.txt
    - echo "Git commit = $CI_COMMIT_SHA" >> dist/build_info.txt
    - echo "Git branch = $CI_COMMIT_BRANCH" >> dist/build_info.txt
    - echo "Git tag = $CI_COMMIT_TAG" >> dist/build_info.txt
    - echo "GitlabCI pipeline ID = $CI_PIPELINE_ID" >> dist/build_info.txt
    # scan go deps -- if scan fails, don't fail build job yet because
    # we still want docker image scan in the next CICD job to run.
    # save a fail to indicate this failure, so it can be checked later
    - jf audit . --go --watches=backend-std-watch  || ([ $? -eq 3 ] && { echo -e "\e[31m!! WARNING !!! Found vulnerabilities in packages that fail the security policy.  The CICD job will fail after Docker image scan.\e[0m" ; touch _go_deps_sec_failed  ; })
  artifacts:
    paths:
      - dist

test:
  tags:
    - k8s-infra-dm
  stage: preimage
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  needs:
    - s2sauth-test-setup
  dependencies:
    - s2sauth-test-setup
  script:
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --reproducible
      --no-push
      --skip-unused-stages
      --target test_ci
      --single-snapshot
      --tar-path=image.tar
      --build-arg "CI_JOB_TOKEN=$CI_JOB_TOKEN"
      --custom-platform linux/amd64

# Deploy to Test Stage by (re-)applying the K8s config from the Git repo.
qa_env_deploy:
  rules:
  - if: '$CI_COMMIT_BRANCH == "master"'
    when: on_success
  - if: '$CI_COMMIT_TAG =~ /v[0-9]{1,5}\.[0-9]{1,5}.[0-9]{1,5}/'
    when: never
  - when: manual
  allow_failure: false
  stage: qa
  tags:
    - k8s-infra-dm
  dependencies:
    - acr_push
  image: phcrcacentral0.azurecr.io/azure-cli:2.63.0
  before_script:
    - az login --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID --federated-token "$(cat $AZURE_FEDERATED_TOKEN_FILE)"
    - apk add --no-cache git curl
    - curl -fsSL https://raw.githubusercontent.com/pressly/goose/master/install.sh | sh
    - goose -dir migrations mysql 'rp-qa:LwTvc=W5eb2ZHXoQHXsPwDWy@tcp(phqa0.mysql.database.azure.com:3306)/reportprocessor?parseTime=true&tls=skip-verify&allowNativePasswords=true' up
    - az aks install-cli
  script:
    - az aks get-credentials -g pockethealth_test -n qa-aks-2
    - kubelogin convert-kubeconfig -l azurecli
    # The k8s gitlab runner (probably inappropriately) defaults to the gitlab-runners namespace. Switch out of it
    - kubectl config set-context --current --namespace default

    - echo "State before deployment:"
    - kubectl get pods -l app=reportprocessor

    # there's a proper Kubernetes way to do this, but hacking it in for now
    - echo "Updating image to tag $CI_COMMIT_SHORT_SHA"
    - sed -i "s/__DOCKER_DIGEST_PLACEHOLDER__/$(cat image_digest)/" deployments/k8s.qa.yml
    - kubectl apply -f deployments/k8s.qa.yml
    - kubectl rollout status deployment reportprocessor
  after_script:
    - echo "Pods:"
    - kubectl get pods -l app=reportprocessor
    - echo "Logs (note there can be multiple pods shown here):"
    - kubectl logs -l app=reportprocessor --prefix=true


prod_cactrl_deploy:
  rules:
    - if: '$CI_COMMIT_TAG =~ /v[0-9]{1,5}\.[0-9]{1,5}.[0-9]{1,5}/'
      when: manual
  stage: prod
  tags:
    - envprod
  dependencies:
    - acr_push
  needs:
    - acr_push
  image: phcrcacentral0.azurecr.io/azure-cli:2.63.0
  before_script:
    - echo "Use link below to login; deployment job will block until authenticated (times out in 15 min)."
    - az login --use-device-code
    - echo "Azure AD credentials OK; deployment approver is <$(az ad signed-in-user show --query userPrincipalName -o tsv)>"
    - apk add --no-cache git curl
    - curl -fsSL https://raw.githubusercontent.com/pressly/goose/master/install.sh | sh
    - goose -dir migrations --certfile migrations/DigiCertGlobalRootCA.crt.pem mysql "patient-eng-mysql-admin:$(az account get-access-token --resource-type oss-rdbms --output tsv --query accessToken)@tcp(ph-sql-main3-cactrl-prod.mysql.database.azure.com)/reportprocessor?parseTime=true&allowCleartextPasswords=true" up
    - az aks install-cli
  script:
    -  az aks get-credentials -g pockethealth_backend_prod -n prod-aks-2
    - kubelogin convert-kubeconfig -l azurecli

    - echo "State before deployment:"
    - kubectl get pods -l app=reportprocessor -n grapefruit

    - echo "Updating image to tag $CI_COMMIT_SHORT_SHA"
    - sed -i "s/__DOCKER_DIGEST_PLACEHOLDER__/$(cat image_digest)/" deployments/k8s.prod-cactrl.yml
    - kubectl apply -f deployments/k8s.prod-cactrl.yml -n grapefruit
    - kubectl rollout status deployment reportprocessor -n grapefruit
    - deployments/release-notes.sh
  after_script:
    - echo "Pods:"
    - kubectl get pods -l app=reportprocessor -n grapefruit
    - echo "Logs (note there can be multiple pods shown here):"
    - kubectl logs -l app=reportprocessor --prefix=true -n grapefruit
    - kubectl get services -n grapefruit
  retry:
    max: 2
    when:
      - runner_system_failure
      - script_failure

prod-uswest_env_deploy:
  rules:
    - if: '$CI_COMMIT_TAG =~ /v[0-9]{1,5}\.[0-9]{1,5}.[0-9]{1,5}/'
      when: manual
  allow_failure: false
  stage: prod
  tags:
    - envprod-uswest
  dependencies:
    - acr_push
  needs:
    - acr_push
  image: phcrcacentral0.azurecr.io/azure-cli:2.63.0
  before_script:
    - az login
    - apk add --no-cache git curl
    - curl -fsSL https://raw.githubusercontent.com/pressly/goose/master/install.sh | sh
    - goose -dir migrations --certfile migrations/DigiCertGlobalRootCA.crt.pem mysql "patient-eng-mysql-admin:$(az account get-access-token --resource-type oss-rdbms --output tsv --query accessToken)@tcp(ph-sql-main1-prod-uswest.mysql.database.azure.com)/reportprocessor?parseTime=true&allowCleartextPasswords=true" up
    - az aks install-cli
  script:
    - az aks get-credentials --resource-group rg-phbackend-prod-uswest --name aks-backend-prod-uswest
    - kubelogin convert-kubeconfig -l azurecli
    - echo "State before deployment:"
    - kubectl get pods -l app=reportprocessor -n grapefruit

    - echo "Updating image to tag $CI_COMMIT_SHORT_SHA"
    - sed -i "s/__DOCKER_DIGEST_PLACEHOLDER__/$(cat image_digest)/" deployments/k8s.prod-uswest.yml
    - kubectl apply -f deployments/k8s.prod-uswest.yml -n grapefruit
    - kubectl rollout status deployment reportprocessor -n grapefruit
  after_script:
    - echo "Pods:"
    - kubectl get pods -l app=reportprocessor -n grapefruit
    - echo "Logs (note there can be multiple pods shown here):"
    - kubectl logs -l app=reportprocessor --prefix=true -n grapefruit
