.PHONY: build reportprocessor rp_linux integ dist gosec docker run_docker clean

#compile and run unit tests
build: reportprocessor

reportprocessor:
	go env -w GOPRIVATE=gitlab.com/pockethealth/*
	go build
	# go vet checks common errors, see go tool vet help for all options.
	# exclude composites, which checks for unkeyed literal struct declarations which we use often.
	go vet -composites=false ./...

# compile for Linux
rp_linux: export GOOS=linux
rp_linux: reportprocessor

# run unit tests
unit: export AZURE_SUBSCRIPTION_ID=************************************
unit: export AZURE_TENANT_ID=************************************
unit: export AZURE_CLIENT_ID=************************************
unit: export AZURE_CLIENT_SECRET=****************************************
unit:
	az login --service-principal -u $(AZURE_CLIENT_ID) -t $(AZURE_TENANT_ID) -p $(AZURE_CLIENT_SECRET)
	az aks get-credentials --resource-group pockethealth_test --name qa-aks-2 -f  $(CURDIR)/tmp/config --overwrite-existing
	kubelogin convert-kubeconfig -l azurecli --kubeconfig $(CURDIR)/tmp/config
	kubectl create token --namespace default sa-reportprocessor --kubeconfig $(CURDIR)/tmp/config > $(CURDIR)/tmp/token.txt
	go test -cover `go list ./... | grep -v /apptest`

# set default test env
PH_TEST_ENV ?= dev

integ: export AZURE_SUBSCRIPTION_ID=************************************
integ: export AZURE_TENANT_ID=************************************
integ: export AZURE_CLIENT_ID=************************************
integ: export AZURE_CLIENT_SECRET=****************************************
integ: export KUBE_CONF_FILE=$(CURDIR)/tmp/config
integ:
# Check if the 'integ' target is invoked in a CI/CD pipeline job environment. $(CI_PROJECT_DIR) is set by the pipeline.
ifeq ($(CI_PROJECT_DIR),)
integ:
	export AZURE_SUBSCRIPTION_ID=************************************
	export AZURE_TENANT_ID=************************************
	export AZURE_CLIENT_ID=************************************
	export AZURE_CLIENT_SECRET=****************************************
	az login --service-principal -u $(AZURE_CLIENT_ID) -t $(AZURE_TENANT_ID) -p $(AZURE_CLIENT_SECRET)
	az aks get-credentials --resource-group pockethealth_test --name qa-aks-2 -f  $(CURDIR)/tmp/config --overwrite-existing
	kubelogin convert-kubeconfig -l azurecli --kubeconfig $(CURDIR)/tmp/config
	kubectl create token --namespace default sa-reportprocessor --kubeconfig $(CURDIR)/tmp/config > $(CURDIR)/tmp/token.txt
	go mod vendor
	DOCKER_BUILDKIT=1 docker build --platform linux/amd64 --target test_local . --progress=plain
endif


# makes distribution suitable to be put into Docker image
dist: rp_linux
	mkdir -p dist
	echo -n 'dev build - last commit was ' > dist/build_info.txt
	git rev-parse --verify HEAD >> dist/build_info.txt
	cp -r reportprocessor docker-entrypoint.sh configs dist
	$(MAKE) gosec
	chmod -R go-rwx dist/

gosec:
	$$(go env GOPATH)/bin/gosec -fmt=json -exclude=G102,G204,G402 -exclude-dir=scripts ./...

NAME   := phcrcacentral0.azurecr.io/reportprocessor
TAG    := $$(git rev-parse --verify HEAD --short)
IMG    := ${NAME}:${TAG}
LATEST := ${NAME}:latest
USERID := 21000
GROUPID:= 21000

#build docker image
docker: clean_bin dist
	az acr login --name phcrcacentral0
	docker build -t ${IMG} --platform linux/amd64 --target rp .
	docker tag ${IMG} ${LATEST}

#build into docker image and run it, connecting to QA dbs
run_docker_qa: export AZURE_SUBSCRIPTION_ID=************************************
run_docker_qa: export AZURE_TENANT_ID=************************************
run_docker_qa: export AZURE_CLIENT_ID=************************************
run_docker_qa: export AZURE_CLIENT_SECRET=****************************************
run_docker_qa: export PH_ENV=qa
run_docker_qa: docker
	docker run --user="${USERID}:${GROUPID}" --env AZURE_SUBSCRIPTION_ID --env AZURE_TENANT_ID --env AZURE_CLIENT_ID --env AZURE_CLIENT_SECRET -p 443:7443 phcrcacentral0.azurecr.io/reportprocessor:latest ${PH_ENV}

clean_bin:
	rm -rf dist reportprocessor

clean: clean_bin
	go clean -testcache
	rm -rf $(CURDIR)/tmp
	docker rm reportprocessor || true
	docker ps -a -q  --filter ancestor=$(NAME):latest
	docker ps -a -q  --filter ancestor=$(NAME):latest_debug
	cd apptest && $(MAKE) clean

start_deps:
	cd apptest && $(MAKE) setup_dev_deps

local: export AZURE_SUBSCRIPTION_ID=************************************
local: export AZURE_TENANT_ID=************************************
local: export AZURE_CLIENT_ID=************************************
local: export AZURE_CLIENT_SECRET=****************************************
local: start_deps docker
	sleep 10
	docker run --user="${USERID}:${GROUPID}" --target rp --env AZURE_SUBSCRIPTION_ID --env AZURE_TENANT_ID --env AZURE_CLIENT_ID --env AZURE_CLIENT_SECRET --network="apptest_apptest" -p 5000:7443 phcrcacentral0.azurecr.io/reportprocessor:latest dev

MIGRATION_DIR := migrations
migration:
	goose -dir ${MIGRATION_DIR} create ${NAME} sql


DEV_DB := "devuser:devpw@tcp(localhost:3306)/reportprocessor?parseTime=true"
QA_DB := "rp-qa:LwTvc=W5eb2ZHXoQHXsPwDWy@tcp(*************:3306)/reportprocessor?parseTime=true"
DBSTRING := $(DEV_DB)

set_db:
ifeq ($(PH_ENV),dev)
	DBSTRING=$(DEV_DB)
else
	DBSTRING=$(QA_DB)
endif

db_up: set_db
	goose -dir ${MIGRATION_DIR} mysql ${DBSTRING} up

db_status: set_db
	goose -dir ${MIGRATION_DIR} mysql ${DBSTRING} status

db_down: set_db
	goose -dir ${MIGRATION_DIR} mysql ${DBSTRING} down

setup_local_mac:
	# this downloads pdftotext
	brew install poppler
