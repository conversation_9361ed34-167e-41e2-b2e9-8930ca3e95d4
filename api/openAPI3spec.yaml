openapi: 3.0.0
info:
  title: reportprocessor
  version: '1.0'
  description: API for PocketHealth report processing.
  contact:
    name: <PERSON>
    email: <EMAIL>
servers:
  - url: 'http://localhost:3000'
paths:
  '/v1/reports/{id}':
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
    get:
      summary: GET report
      tags:
        - reports
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      operationId: get-reports-id
      description: 'Get report as PNG, PDF, or HTML according to the Accept header'
      parameters:
        - schema:
            type: string
            enum:
              - image/png
              - application/pdf
              - text/html
          in: header
          name: Accept
          description: Format to return the report in. Default image/png.
        - schema:
            type: boolean
          in: query
          name: definitions
          description: 'only applies for Accept=text/html, and controls whether the report is tagged and returned with terms and definitions'
  '/v1/reports/{id}/taggedhtml':
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
      - schema:
          type: string
          default: ""
        in: query
        name: subdictionary
    get:
      summary: get report html with defined terms
      tags:
        - reports
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaggedHTML'
      operationId: get-reports-id-taggedhtml
      description: |-
        Returns report HTML with defined terms wrapped in <span>s with the class "phdefinition"
        Also returns a dictionary of tagged terms with definitions
      security:
        - APIkey: []
  '/v1/reports/{id}/insights/followup':
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
    get:
      summary: get a followup insight for a report
      tags: 
        - reports
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FollowupResponse'
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      operationId: get-v1-reports-reportId-insights-followup
      description: |-
        Grabs PDF report from storage, parse into raw text, and write to `reportinsightsdata` storage container 
        Then, call report insights to generate followup information
  '/v1/reports/insights/followups':
    post:
      summary: Get follow insights for a list of report Ids
      tags: 
        - reports
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MultiReportFollowupResponse'
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      operationId: get-v1-reports-insights-followups
      description: |-
        does the same as `/{id}/insights/followup`,
        but can handle multiple report ids
  '/v1/reports/{id}/insights/questions':
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
    get:
      summary: Get questions for your doctor
      tags:
        - reports
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionsResponse'
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      operationId: get-reports-reportId-insights-questions
      description: |-
        Grabs PDF report from storage, parse into raw text, and write to `reportinsightsdata` storage container
        Then, call report insights to generate questions for your doctor

  '/v1/reports/{id}/insights/organviz':
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
    get:
      summary: Get organ visualization data
      tags:
        - reports
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganvizResponse'
        '400':
          description: Invalid model
        '401':
          description: Unauthorized
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      operationId: get-reports-reportId-insights-organviz
      description: |-
        Grabs PDF report from storage, parse into raw text, and write to `reportinsightsdata` storage container
        Then, call report insights to generate organ visualization data
      security:
        - APIkey: []

components:
  schemas:
    TaggedHTML:
      title: TaggedHTML
      type: object
      properties:
        defs:
          type: object
          description: dictionary of string terms to string definitions
          additionalProperties:
            type: string
        report:
          type: string
      example:
        defs:
          term1: here is term1's definition
          term2: here is term2's definition
        report: <div>report as html</div>

    QuestionsResponse:
      title: QuestionsResponse
      type: object
      properties:
        questions:
          type: array
          items:
            type: string

    FollowupResponse:
      title: FollowupResponse
      type: object
      properties:
        exists:
          type: boolean
          description: whether the report contains a followup or not
        occurrences:
          type: array
          items:
            $ref: '#/components/schemas/FollowupOccurrence'

    FollowupOccurrence:
      title: FollowupOccurrence
      type: object
      properties:
        context:
          type: string
          description: Verbatim description of the follow-up found in the report.
        min_time_frame:
          type: number
          description: Report the earliest time frame for follow-up (e.g., number of days, months, years)
        max_time_frame:
          type: number
          description: Report the latest time frame for follow-up (e.g., number of days, months, years)
        time_frame_unit:
          type: string
          description: Report the time unit used for the latest time frame (e.g., 'day,' 'month,' 'year')
        category:
          type: string
          description: Bucket a follow up into one of the categories
      required:
        - context

    MultiReportFollowupResponse:
      type: object
      properties:
        reportId:
          $ref: '#/components/schemas/FollowupResponse'
        
    OrganvizResponse:
      title: OrganvizResponse
      type: object
      properties:
        model_used:
          type: string
          enum:
            - medsam
            - xray
          description: The model used to generate the organviz data
        organs:
          type: array
          items:
            $ref: '#/components/schemas/OrganVisualization'
      description: Contains organ segmentation data

    OrganVisualization:
      title: OrganVisualization
      type: object
      properties:
        body_part:
          type: string
          description: Name of the organ (e.g. "liver", "spleen")
        model_version:
          type: string
          description: Version of the segmentation model (e.g. "0.6")
        segmentation:
          type: string
          description: Base64-encoded segmentation mask 
        status:
          type: string
          description: Processing status of the segmentation

  securitySchemes:
    APIkey:
      name: Authorization
      type: apiKey
      in: query

security:
  - APIkey: []
