# Report Processor

![Pipeline badge](https://gitlab.com/pockethealth/reportprocessor/badges/master/pipeline.svg)

## Running locally

This service uses a custom base image from `baseImage/`, stored in the ph registry. To be able to build the docker image, run 
```
az login
az acr login --name phcrcacentral0
```
acr login credentials only last 3 hours


Run to bring up local database, make sure db is up to date, and start up local service connected to it:
`make local`.

## DB Changes

We are using `pressly/goose` to manage DB migrations.

Dl and install:
`go get -u github.com/pressly/goose/v3/cmd/goose@v3.0.1`
NOTE: must pin to 3.0.1 because of compatibility with our go version.

To create a new migration, run
`make migration NAME=my_migration`

This will create a file in /migrations. Edit this file - after the up StatementBegin, add the SQL to perform the migration.
If applicable, under the goose down section, add the SQL to revert the migration.

To run all pending migrations on a dev db, run 
`make db_up`

To see migration status on a dev db, run 
`make db_status`

To run these on qa, use 
`make db_up PH_ENV=qa`

`goose` has several more commands, such as up-by-one or down-by-one, but the ones added to the makefile will probably be most often used. See the goose help docs for more info.

TODO: Figure out more straightforward prod access for those with creds.

In prod, with `goose`, just using the connection string will result in an error that says SSL is required.
According to [Azure](https://docs.microsoft.com/en-us/azure/mysql/howto-configure-ssl), you can use the cert from this site to do so.
With goose, the command looks like
`goose -dir migrations -certfile /path/to/cert/downloaded/from/above/link mysql <connstring> up`
## Infrastructure

See [deployments/infra/README.md](deployments/infra/README.md).
