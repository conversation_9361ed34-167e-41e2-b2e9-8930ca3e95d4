# Report Reader Images

The images that are planned to be uploaded to the `phstpublicassets` storage account should be downloaded locally and into the `rr_images` folder.

The images should have the format <em>term name</em>. Image files should have the format <em>term.file-extension</em>, i.e. <em>aortic aneurism.png</em>.

Images that are being uploaded concurrently should be from the same source and have the same modified status. A modified image from source A should not be uploaded at the same time as a non-modified image from source A, nor should an image from source A and source B be uploaded at the same time.

## To Run:

- Add folder of images to be uploaded (called `rr_images`) to `reportprocessor` directory.
- `cd` to the `upload_rr_images` directory.
- Get the azure client url \(with SAS token\) by generating one, or asking someone who has permissions to generate it (ie Grace).
- Also acquire `reportprocessor` database connection string.
- Finally, run the command below:

```
go build
./upload_rr_images <image_source> <is_modified> '<az_client_url>' '<db_connection_string>'
```

- Afterwards, can remove `rr_images` folder from repo before pushing migration (not needed anymore).
- Make migration by running `make migration NAME=insert_new_image_info`. Then populate migration by copying output from migration script & previous `insert_new_image_info` migrations for structure
- Can then make MR & push to QA to see any updates in app.

**Note**

You may also require someone to manually flush the CDN to see updates if old images have been updated. After CDN flush clear your browser cache and hard refresh to see updates.

**Disclaimers - likely not important since migrations have been ran & new images are being made in-house**

Make sure that you have ran the migrations that add the new citations table and image_link columns.
If any links + citations have been added / modified, make a new migration, following the format of `insert_new_image_info.sql` and use find+replace to format the insert.

## Update:

Since our images are now created in-house, we cite these images by using PocketHealth as the image source. Thus, the command you will likely be running is

```
go build
./upload_rr_images PocketHealth 0 '<az_client_url>' '<db_connection_string>'
```

This will now also return print the down migration for these images so you can copy it!
