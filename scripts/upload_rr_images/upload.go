package main

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/blob"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/blockblob"
	_ "github.com/go-sql-driver/mysql"
	"github.com/samber/lo"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/phutils/v10/pkg/random"
)

func main() {
	args := os.Args[1:]

	if len(args) < 4 {
		fmt.Fprintf(
			os.Stderr,
			"usage: %s <image_source> <is_modified> '<az_client_url>' '<sql_connection_string>'",
			os.Args[0],
		)
		return
	}

	// create azblob client
	azClientUrl := args[2]
	client, err := azblob.NewClientWithNoCredential(azClientUrl, nil)
	if err != nil {
		logrus.WithError(err).
			Fatal("failed to make blob store client, make sure it has a valid SAS token")
	}

	logrus.Info("successfully created azblob client")

	// connect to DB
	rpConnStr := args[3]
	db, err := sql.Open("mysql", rpConnStr)
	if err != nil {
		logrus.WithError(err).Fatal("unable to sql.Open")
	}
	err = db.Ping()
	if err != nil {
		logrus.WithError(err).Fatal("unable to ping reportprocessor db")
	}

	logrus.Info("successfully connected to db")

	// create citation mapping: {<image_source> : [<unmodified_citation_id>, <modified_citation_id>], ...}
	citationMap := map[string][]int{
		"servier":      {1, 2},
		"PocketHealth": {3},
	}
	knownSources := []string{"servier", "PocketHealth"}

	// get citation id of the uploaded images
	uploadingSource := args[0]
	citationIds, ok := citationMap[uploadingSource]
	if !ok {
		fmt.Fprintf(
			os.Stderr,
			"expected image to be from the sources: %v, got %s",
			knownSources, uploadingSource,
		)
		return
	}
	isModified, err := strconv.ParseBool(args[1])
	if err != nil {
		logrus.WithError(err).Fatal("must be a T/F value")
	}
	citationId := citationIds[0]
	if isModified {
		citationId = citationIds[1]
	}

	// read locally downloaded images
	imageFilePath := "../../rr_images/"
	images, err := os.ReadDir(imageFilePath)
	if err != nil {
		logrus.WithError(err).Fatal("failed to read directory")
	}
	if len(images) == 0 {
		logrus.WithError(err).Fatal("no images to upload")
	}

	var imageInfoValues []string

	for _, image := range images {
		fileName := image.Name()
		imageName := strings.Split(fileName, ".")[0]
		var imageLink, blobName string

		// check if the images are already uploaded
		var existingImageLink string
		err := db.QueryRow("SELECT image_link FROM term_definitions WHERE term = ?", imageName).
			Scan(&existingImageLink)
		if err != nil {
			// could not find an existing link, generate a randomId
			randomId, err := random.GenerateRandomString(6)
			if err != nil {
				logrus.WithError(err).Fatal("failed making random id")
			}
			blobName = fmt.Sprintf("rr_%s", randomId)
			imageLink = fmt.Sprintf("assets.pocket.health/%s", blobName)
		} else {
			blobName = strings.Split(existingImageLink, "/")[1]
			imageLink = existingImageLink
		}

		// image info to insert into the temp table
		imageInfo := fmt.Sprintf("('%s', '%s', '%d')", imageName, imageLink, citationId)
		imageInfoValues = append(imageInfoValues, imageInfo)

		// open image and upload to container
		file, err := os.Open(fmt.Sprintf(imageFilePath + fileName))
		if err != nil {
			logrus.WithError(err).Fatalf("failed to open image %s", fileName)
		}
		// close the files
		defer func() {
			err := file.Close()
			if err != nil {
				logrus.Fatal("error closing files")
			}
		}()

		_, err = client.UploadFile(context.TODO(), "", blobName, file, &blockblob.UploadFileOptions{
			HTTPHeaders: &blob.HTTPHeaders{
				BlobContentType: to.Ptr("image/png"),
			},
		})
		if err != nil {
			logrus.WithError(err).Fatal("failed to upload to blob client")
		}
	}

	imageInfoInserts := strings.Join((imageInfoValues), ", ")
	fmt.Printf(
		"INSERT INTO temp_image_info (term, image_link, citation_id) VALUES %s;\n\n",
		imageInfoInserts,
	)

	// Generate & Print Down Migration
	downMigration := `UPDATE term_definitions SET image_link = NULL, citation_id = NULL WHERE term`
	terms := lo.RepeatBy(
		len(images),
		func(idx int) string { return "'" + strings.ToLower(strings.Split(images[idx].Name(), ".")[0]) + "'" },
	)
	fmt.Printf("%s IN (%s);", downMigration, strings.Join(terms, ","))
}
