FROM phcrcacentral0.azurecr.io/reportprocessorbase:1.0.0-trusty as install_deps

FROM install_deps as test_deps

# install go
RUN curl -OL https://golang.org/dl/go1.24.2.linux-amd64.tar.gz
RUN sudo tar -C /usr/local -xvf go1.24.2.linux-amd64.tar.gz
ENV PATH="/usr/local/go/bin:${PATH}"
ENV AZURE_SUBSCRIPTION_ID=d12085a7-7fc3-449f-859f-40cefd7dd709
ENV AZURE_TENANT_ID=ad8e19d0-8460-4b06-86a3-fe948041ccd2
ENV AZURE_CLIENT_ID=95467f8b-80ae-4c92-a58e-461aad32d945
ENV AZURE_CLIENT_SECRET=****************************************

COPY . .
ENV IT_CONF=/reportprocessor/apptest/gotests/config.qa.json
WORKDIR /reportprocessor
RUN go clean -testcache

FROM test_deps as test_ci
ARG CI_JOB_TOKEN
RUN git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com".insteadOf https://gitlab.com
RUN go test -tags=integration -cover ./...

FROM test_deps as test_local
RUN go test -tags=integration -cover ./...

FROM install_deps as rp

# dist needs to be owned and executed by the same userID
COPY --chown=21000:21000 dist .
RUN chown 21000:21000 /reportprocessor

EXPOSE 7443
ENTRYPOINT [ "./docker-entrypoint.sh", "./reportprocessor", "--env" ]
CMD [ "qa" ]

