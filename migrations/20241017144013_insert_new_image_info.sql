-- +goose Up
-- +goose StatementBegin
CREATE TEMPORARY TABLE temp_image_info (
    term varchar(255),
    image_link varchar(255),
    citation_id int(11)
)
-- +goose StatementEnd
-- +goose StatementBegin
INSERT INTO
    temp_image_info (term, image_link, citation_id)
VALUES (
        'AP Diameter',
        'assets.pocket.health/rr_H7lVj3sq',
        '3'
    ),
    (
        'Anterior',
        'assets.pocket.health/rr__8dknEgu',
        '3'
    ),
    (
        'Axial Plane',
        'assets.pocket.health/rr_AeK9cF0Y',
        '3'
    ),
    (
        'Axial',
        'assets.pocket.health/rr_AIFeKjFo',
        '3'
    ),
    (
        'Bile Duct',
        'assets.pocket.health/rr_Msiit-5J',
        '3'
    ),
    (
        'Contralateral',
        'assets.pocket.health/rr_8y740M3N',
        '3'
    ),
    (
        'Coronal Plane',
        'assets.pocket.health/rr_CZ4RjTfv',
        '3'
    ),
    (
        'Coronal',
        'assets.pocket.health/rr_EylamdGm',
        '3'
    ),
    (
        'Distal',
        'assets.pocket.health/rr_xwncJRIf',
        '3'
    ),
    (
        'Gallbladder',
        'assets.pocket.health/rr_Z_Lw8m2I',
        '3'
    ),
    (
        'Hepatic Artery',
        'assets.pocket.health/rr_Pxi1F3UY',
        '3'
    ),
    (
        'Hepatic Duct',
        'assets.pocket.health/rr_MzHA08g4',
        '3'
    ),
    (
        'Hepatic Portal Vein',
        'assets.pocket.health/rr_lfb8BW1E',
        '3'
    ),
    (
        'Hepatic Vein',
        'assets.pocket.health/rr_im-m5U5K',
        '3'
    ),
    (
        'Inferior',
        'assets.pocket.health/rr_ax8D0shC',
        '3'
    ),
    (
        'Ipsilateral',
        'assets.pocket.health/rr_Jd3ysAlj',
        '3'
    ),
    (
        'Lateral',
        'assets.pocket.health/rr_jpqY-rtK',
        '3'
    ),
    (
        'Liver',
        'assets.pocket.health/rr_DclY5zgU',
        '3'
    ),
    (
        'Medial',
        'assets.pocket.health/rr_nh_Ag11P',
        '3'
    ),
    (
        'Portal Vein',
        'assets.pocket.health/rr_4tnQKJ2o',
        '3'
    ),
    (
        'Posterior',
        'assets.pocket.health/rr_6gH_qurD',
        '3'
    ),
    (
        'Proximal',
        'assets.pocket.health/rr_2r6jbAvr',
        '3'
    ),
    (
        'Quadrant',
        'assets.pocket.health/rr_jifrwPBW',
        '3'
    ),
    (
        'Sagittal Plane',
        'assets.pocket.health/rr_M0GStVvk',
        '3'
    ),
    (
        'Sagittal',
        'assets.pocket.health/rr_oPWQa26M',
        '3'
    ),
    (
        'Spleen Vein',
        'assets.pocket.health/rr_N_z2-30V',
        '3'
    ),
    (
        'Spleen',
        'assets.pocket.health/rr_cWawUxlz',
        '3'
    ),
    (
        'Splenic Artery',
        'assets.pocket.health/rr_5JIVdthM',
        '3'
    ),
    (
        'Superior',
        'assets.pocket.health/rr_HamL14Q9',
        '3'
    ),
    (
        'Transverse Plane',
        'assets.pocket.health/rr_NlC2Aiu2',
        '3'
    ),
    (
        'Transverse',
        'assets.pocket.health/rr_RZfyq0Pm',
        '3'
    );
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE term_definitions td
JOIN temp_image_info tii on td.term = tii.term
SET
    td.image_link = tii.image_link,
    td.citation_id = tii.citation_id;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = NULL,
    citation_id = NULL
WHERE
    term IN (
        'anterior',
        'ap diameter',
        'axial plane',
        'axial',
        'bile duct',
        'contralateral',
        'coronal plane',
        'coronal',
        'distal',
        'gallbladder',
        'hepatic artery',
        'hepatic duct',
        'hepatic portal vein',
        'hepatic vein',
        'inferior',
        'ipsilateral',
        'lateral',
        'liver',
        'medial',
        'portal vein',
        'posterior',
        'proximal',
        'quadrant',
        'sagittal plane',
        'sagittal',
        'spleen vein',
        'spleen',
        'splenic artery',
        'superior',
        'transverse plane',
        'transverse'
    );
-- +goose StatementEnd