-- +goose Up
-- +goose StatementBegin
ALTER TABLE report_image_citations
DROP COLUMN `citation_html`,
ADD COLUMN `source_name` VARCHAR(255) NOT NULL,
ADD COLUMN `modified` BOOLEAN NOT NULL,
ADD COLUMN `source_anchor` TEXT NOT NULL,
ADD COLUMN `license_anchor` TEXT NOT NULL;
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE report_image_citations
SET 
    `source_name` = 'Servier', 
    `modified` = 0, 
    `source_anchor`= '<a href="https://smart.servier.com/" target="_blank">Servier Medical Art</a>', 
    `license_anchor` = '<a href="https://creativecommons.org/licenses/by/4.0/" target="_blank">CC BY 4.0</a>'
WHERE `citation_id` = 1;
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE report_image_citations
SET 
    `source_name` = 'Servier', 
    `modified` = 1, 
    `source_anchor` = '<a href="https://smart.servier.com/" target="_blank">Servier Medical Art</a>', 
    `license_anchor` = '<a href="https://creativecommons.org/licenses/by/4.0/" target="_blank">CC BY 4.0</a>'
WHERE `citation_id` = 2;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE `report_image_citations`
ADD COLUMN `citation_html` TEXT NOT NULL,
DROP COLUMN `source_name`,
DROP COLUMN `modified`,
DROP COLUMN `source_anchor`,
DROP COLUMN `license_anchor`;
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE report_image_citations
SET `citation_html` = 'Image from <a href="https://smart.servier.com/" target="_blank">Servier Medical Art</a> by Servier. <a href="https://creativecommons.org/licenses/by/4.0/" target="_blank">CC BY 4.0</a>'
where `citation_id` = 1;
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE report_image_citations
SET `citation_html` = 'Image from <a href="https://smart.servier.com/" target="_blank">Servier Medical Art</a> by Servier, modified. <a href="https://creativecommons.org/licenses/by/4.0/" target="_blank">CC BY 4.0</a>'
where `citation_id` = 2;
-- +goose StatementEnd