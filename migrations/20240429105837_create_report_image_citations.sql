-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS `report_image_citations` (
    `citation_id` INT(11) NOT NULL AUTO_INCREMENT,
    `citation_html` TEXT NOT NULL,
    PRIMARY KEY(`citation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO `report_image_citations` (citation_html)
VALUES 
('Image from <a href="https://smart.servier.com/">Servier Medical Art</a> by Servier. <a href="https://creativecommons.org/licenses/by/4.0/">CC BY 4.0</a>'),
('Image from <a href="https://smart.servier.com/">Servier Medical Art</a> by Servier, modified. <a href="https://creativecommons.org/licenses/by/4.0/">CC BY 4.0</a>');
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `report_image_citations`;
-- +goose StatementEnd
