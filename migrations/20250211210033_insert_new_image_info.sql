-- +goose Up
-- +goose StatementBegin
CREATE TEMPORARY TABLE temp_image_info (
    term varchar(255),
    image_link varchar(255),
    citation_id int(11)
);
-- +goose StatementEnd
-- +goose StatementBegin
INSERT INTO
    temp_image_info (term, image_link, citation_id)
VALUES (
        'Airway',
        'assets.pocket.health/rr_s9VKGxSY',
        '3'
    ),
    (
        'Anterior',
        'assets.pocket.health/rr__8dknEgu',
        '3'
    ),
    (
        'Aortic arch',
        'assets.pocket.health/rr_OM9xpPKJ',
        '3'
    ),
    (
        'Appendix',
        'assets.pocket.health/rr_vqrhAMX2',
        '3'
    ),
    (
        'Articular cartilage',
        'assets.pocket.health/rr_fYuj_17q',
        '3'
    ),
    (
        'Bone marrow',
        'assets.pocket.health/rr_22NU89gi',
        '3'
    ),
    (
        'Bone',
        'assets.pocket.health/rr_Fsxjmmuh',
        '3'
    ),
    (
        'Bones',
        'assets.pocket.health/rr_0cyB33cN',
        '3'
    ),
    (
        'Bony',
        'assets.pocket.health/rr_MSjcFESe',
        '3'
    ),
    (
        'Bowel',
        'assets.pocket.health/rr_Md-Xrwiu',
        '3'
    ),
    (
        'Cardiac silhouette',
        'assets.pocket.health/rr_45FBJjia',
        '3'
    ),
    (
        'Cardiac',
        'assets.pocket.health/rr_xz3pPP0X',
        '3'
    ),
    (
        'Coronary arteries',
        'assets.pocket.health/rr_I60H00Te',
        '3'
    ),
    (
        'Coronary artery',
        'assets.pocket.health/rr_hMeARp-b',
        '3'
    ),
    (
        'Descending Colon',
        'assets.pocket.health/rr_onGa797I',
        '3'
    ),
    (
        'Distal',
        'assets.pocket.health/rr_xwncJRIf',
        '3'
    ),
    (
        'Duodenum',
        'assets.pocket.health/rr_SI8kw_-I',
        '3'
    ),
    (
        'Esoph',
        'assets.pocket.health/rr_DoK_ywak',
        '3'
    ),
    (
        'Esophagus',
        'assets.pocket.health/rr_kQdXu2RF',
        '3'
    ),
    (
        'Femur length',
        'assets.pocket.health/rr_FCi5r0Vw',
        '3'
    ),
    (
        'Femur',
        'assets.pocket.health/rr_dqcKuixm',
        '3'
    ),
    (
        'Fracture',
        'assets.pocket.health/rr_PKocH0kX',
        '3'
    ),
    (
        'Fractures',
        'assets.pocket.health/rr_K1wKaxLu',
        '3'
    ),
    (
        'Gastrointestinal Tract',
        'assets.pocket.health/rr_fn3pJIwZ',
        '3'
    ),
    (
        'Gi Tract',
        'assets.pocket.health/rr_0lIXylde',
        '3'
    ),
    (
        'Gi',
        'assets.pocket.health/rr_CoOcswHK',
        '3'
    ),
    (
        'Heart',
        'assets.pocket.health/rr_RkAat9Hd',
        '3'
    ),
    (
        'Hepatic',
        'assets.pocket.health/rr_-G_3elgl',
        '3'
    ),
    (
        'Horizontal fissure',
        'assets.pocket.health/rr_VdZarfFp',
        '3'
    ),
    (
        'Inferior',
        'assets.pocket.health/rr_ax8D0shC',
        '3'
    ),
    (
        'Large Intestine',
        'assets.pocket.health/rr_tRbAD7Hq',
        '3'
    ),
    (
        'Lateral',
        'assets.pocket.health/rr_jpqY-rtK',
        '3'
    ),
    (
        'Liver',
        'assets.pocket.health/rr_DclY5zgU',
        '3'
    ),
    (
        'Lung parenchyma',
        'assets.pocket.health/rr_x1Pf0xIB',
        '3'
    ),
    (
        'Lung',
        'assets.pocket.health/rr_dgXu2-yL',
        '3'
    ),
    (
        'Marrow',
        'assets.pocket.health/rr_g81NO5Cy',
        '3'
    ),
    (
        'Medial',
        'assets.pocket.health/rr_nh_Ag11P',
        '3'
    ),
    (
        'Midline Shift',
        'assets.pocket.health/rr_N5vPr9y6',
        '3'
    ),
    (
        'Osseous',
        'assets.pocket.health/rr_4LsnTquf',
        '3'
    ),
    (
        'Pancreas',
        'assets.pocket.health/rr_19-epSDG',
        '3'
    ),
    (
        'Portal vein',
        'assets.pocket.health/rr_4tnQKJ2o',
        '3'
    ),
    (
        'Posterior',
        'assets.pocket.health/rr_6gH_qurD',
        '3'
    ),
    (
        'Proximal',
        'assets.pocket.health/rr_2r6jbAvr',
        '3'
    ),
    (
        'Pulmonary arteries',
        'assets.pocket.health/rr_qfcxdGYf',
        '3'
    ),
    (
        'Pulmonary',
        'assets.pocket.health/rr_zgTMV2R-',
        '3'
    ),
    (
        'Rectal',
        'assets.pocket.health/rr_fJc-QwNI',
        '3'
    ),
    (
        'Rectum',
        'assets.pocket.health/rr_tKa6C_JZ',
        '3'
    ),
    (
        'Sigmoid Colon',
        'assets.pocket.health/rr_y-qUdgRY',
        '3'
    ),
    (
        'Small Intestine',
        'assets.pocket.health/rr_o6fNxqR6',
        '3'
    ),
    (
        'Small bowel',
        'assets.pocket.health/rr_-b7bvUGp',
        '3'
    ),
    (
        'Spleen',
        'assets.pocket.health/rr_cWawUxlz',
        '3'
    ),
    (
        'Splenic vein',
        'assets.pocket.health/rr_pm7MRJfj',
        '3'
    ),
    (
        'Superior',
        'assets.pocket.health/rr_HamL14Q9',
        '3'
    ),
    (
        'Trachea',
        'assets.pocket.health/rr_wpWU6VlB',
        '3'
    );
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE term_definitions td
JOIN temp_image_info tii on td.term = tii.term
SET
    td.image_link = tii.image_link,
    td.citation_id = tii.citation_id;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = NULL,
    citation_id = NULL
WHERE
    term IN (
        'airway',
        'anterior',
        'aortic arch',
        'appendix',
        'articular cartilage',
        'bone marrow',
        'bone',
        'bones',
        'bony',
        'bowel',
        'cardiac silhouette',
        'cardiac',
        'coronary arteries',
        'coronary artery',
        'descending colon',
        'distal',
        'duodenum',
        'esoph',
        'esophagus',
        'femur length',
        'femur',
        'fracture',
        'fractures',
        'gastrointestinal tract',
        'gi tract',
        'gi',
        'heart',
        'hepatic',
        'horizontal fissure',
        'inferior',
        'large intestine',
        'lateral',
        'liver',
        'lung parenchyma',
        'lung',
        'marrow',
        'medial',
        'midline shift',
        'osseous',
        'pancreas',
        'portal vein',
        'posterior',
        'proximal',
        'pulmonary arteries',
        'pulmonary',
        'rectal',
        'rectum',
        'sigmoid colon',
        'small intestine',
        'small bowel',
        'spleen',
        'splenic vein',
        'superior',
        'trachea'
    );
-- +goose StatementEnd