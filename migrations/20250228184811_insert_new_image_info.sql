-- +goose Up
-- +goose StatementBegin
CREATE TEMPORARY TABLE temp_image_info (
    term varchar(255),
    image_link varchar(255),
    citation_id int(11)
);
-- +goose StatementEnd
-- +goose StatementBegin
INSERT INTO
    temp_image_info (term, image_link, citation_id)
VALUES (
        'Ac joint',
        'assets.pocket.health/rr_SBscT0P_',
        '3'
    ),
    (
        'Acromioclavicular joint',
        'assets.pocket.health/rr_XyNYoZhn',
        '3'
    ),
    (
        'Aorta',
        'assets.pocket.health/rr_so926Cn_',
        '3'
    ),
    (
        'Aortic aneurysm',
        'assets.pocket.health/rr_hVJ5pkeu',
        '3'
    ),
    (
        'Aortic arch',
        'assets.pocket.health/rr_OM9xpPKJ',
        '3'
    ),
    (
        'Aortic root',
        'assets.pocket.health/rr_ekiciend',
        '3'
    ),
    (
        'Aortic',
        'assets.pocket.health/rr_jKtlXb0J',
        '3'
    ),
    (
        'Art',
        'assets.pocket.health/rr_rFI3mI6D',
        '3'
    ),
    (
        'Arteries',
        'assets.pocket.health/rr_Nf7xE88y',
        '3'
    ),
    (
        'Artery',
        'assets.pocket.health/rr_lNJ2Vt6m',
        '3'
    ),
    (
        'Articular cartilage',
        'assets.pocket.health/rr_fYuj_17q',
        '3'
    ),
    (
        'Articular surface',
        'assets.pocket.health/rr_kYaJZeLZ',
        '3'
    ),
    (
        'Bicipital groove',
        'assets.pocket.health/rr_u8O1s5Ja',
        '3'
    ),
    (
        'Bilat',
        'assets.pocket.health/rr_RaJ8I6fR',
        '3'
    ),
    (
        'Bilateral',
        'assets.pocket.health/rr_Zffrvuk1',
        '3'
    ),
    (
        'Bilaterally',
        'assets.pocket.health/rr_0U7uXGmv',
        '3'
    ),
    (
        'Bone marrow',
        'assets.pocket.health/rr_22NU89gi',
        '3'
    ),
    (
        'Bone',
        'assets.pocket.health/rr_Fsxjmmuh',
        '3'
    ),
    (
        'Bones',
        'assets.pocket.health/rr_0cyB33cN',
        '3'
    ),
    (
        'Bony',
        'assets.pocket.health/rr_MSjcFESe',
        '3'
    ),
    (
        'Cardiac',
        'assets.pocket.health/rr_xz3pPP0X',
        '3'
    ),
    (
        'Carotid arteries',
        'assets.pocket.health/rr_-Myf10AH',
        '3'
    ),
    (
        'Carotid artery',
        'assets.pocket.health/rr_dGjL8meq',
        '3'
    ),
    (
        'Clavicle',
        'assets.pocket.health/rr_31T71lhy',
        '3'
    ),
    (
        'Contralateral',
        'assets.pocket.health/rr_8y740M3N',
        '3'
    ),
    (
        'Coronary arteries',
        'assets.pocket.health/rr_I60H00Te',
        '3'
    ),
    (
        'Coronary artery',
        'assets.pocket.health/rr_hMeARp-b',
        '3'
    ),
    (
        'Heart',
        'assets.pocket.health/rr_RkAat9Hd',
        '3'
    ),
    (
        'Humerus',
        'assets.pocket.health/rr_GKtJWKO7',
        '3'
    ),
    (
        'Ipsilateral',
        'assets.pocket.health/rr_Jd3ysAlj',
        '3'
    ),
    (
        'Joint',
        'assets.pocket.health/rr_0odhFg5z',
        '3'
    ),
    (
        'Joints',
        'assets.pocket.health/rr_oZ6fSuiA',
        '3'
    ),
    (
        'Jt',
        'assets.pocket.health/rr_veTq6pul',
        '3'
    ),
    (
        'Left Clavicle',
        'assets.pocket.health/rr_GyDqO_7W',
        '3'
    ),
    (
        'Left Scapula',
        'assets.pocket.health/rr_MExupxOO',
        '3'
    ),
    (
        'Marrow',
        'assets.pocket.health/rr_g81NO5Cy',
        '3'
    ),
    (
        'Multiplanar',
        'assets.pocket.health/rr_2wb1IU8g',
        '3'
    ),
    (
        'Osseous',
        'assets.pocket.health/rr_4LsnTquf',
        '3'
    ),
    (
        'Pulmonary arteries',
        'assets.pocket.health/rr_qfcxdGYf',
        '3'
    ),
    (
        'Right Clavicle',
        'assets.pocket.health/rr_b3sx6hs6',
        '3'
    ),
    (
        'Right Scapula',
        'assets.pocket.health/rr_e_gNhJsl',
        '3'
    ),
    (
        'Shoulder',
        'assets.pocket.health/rr_fhjWc7Sz',
        '3'
    ),
    (
        'Superior vena cava',
        'assets.pocket.health/rr_jiUBsVzW',
        '3'
    ),
    (
        'Unilat',
        'assets.pocket.health/rr__jWlJQbF',
        '3'
    ),
    (
        'Unilateral',
        'assets.pocket.health/rr_Z8Drg1j5',
        '3'
    ),
    (
        'Ureter',
        'assets.pocket.health/rr_hmF3bP7e',
        '3'
    ),
    (
        'Vein',
        'assets.pocket.health/rr_cSiAPIm3',
        '3'
    ),
    (
        'Veins',
        'assets.pocket.health/rr_C6BSTbFk',
        '3'
    ),
    (
        'Venous',
        'assets.pocket.health/rr_1QrkgNpB',
        '3'
    );
-- +goose StatementEnd
-- +goose StatementBegin
UPDATE term_definitions td
JOIN temp_image_info tii on td.term = tii.term
SET
    td.image_link = tii.image_link,
    td.citation_id = tii.citation_id;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = NULL,
    citation_id = NULL
WHERE
    term IN (
        'ac joint',
        'acromioclavicular joint',
        'aorta',
        'aortic aneurysm',
        'aortic arch',
        'aortic root',
        'aortic',
        'art',
        'arteries',
        'artery',
        'articular cartilage',
        'articular surface',
        'bicipital groove',
        'bilat',
        'bilateral',
        'bilaterally',
        'bone marrow',
        'bone',
        'bones',
        'bony',
        'cardiac',
        'carotid arteries',
        'carotid artery',
        'clavicle',
        'contralateral',
        'coronary arteries',
        'coronary artery',
        'heart',
        'humerus',
        'ipsilateral',
        'joint',
        'joints',
        'jt',
        'left clavicle',
        'left scapula',
        'marrow',
        'multiplanar',
        'osseous',
        'pulmonary arteries',
        'right clavicle',
        'right scapula',
        'shoulder',
        'superior vena cava',
        'unilat',
        'unilateral',
        'ureter',
        'vein',
        'veins',
        'venous'
    );
-- +goose StatementEnd