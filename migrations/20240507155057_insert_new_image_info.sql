-- +goose Up
-- +goose StatementBegin
CREATE TEMPORARY TABLE temp_image_info(
		term varchar(255),
		image_link varchar(255),
		citation_id int(11)
		)
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO temp_image_info (term, image_link, citation_id) VALUES 
	('aorta', 'assets.pocket.health/rr_so926Cn_', '1'),
	('aortic aneurysm', 'assets.pocket.health/rr_k7pCJa1O', '1'),
	('arteries', 'assets.pocket.health/rr_Nf7xE88y', '1'),
	('bladder', 'assets.pocket.health/rr_AojT7wYl', '1'),
	('brain', 'assets.pocket.health/rr_JHwKqKs0', '1'),
	('breast', 'assets.pocket.health/rr_EWPsxhmi', '1'),
	('fat', 'assets.pocket.health/rr_NH2wwXGS', '1'),
	('femur', 'assets.pocket.health/rr_dqcKuixm', '1'),
	('fetus', 'assets.pocket.health/rr_gqm8d3FU', '1'),
	('foot', 'assets.pocket.health/rr_8Zt9LO1I', '1'),
	('gallbladder', 'assets.pocket.health/rr_Z_Lw8m2I', '1'),
	('heart', 'assets.pocket.health/rr_RkAat9Hd', '1'),
	('hiatal hernia', 'assets.pocket.health/rr_l5PrSuNR', '1'),
	('kidney', 'assets.pocket.health/rr_JAaKY26n', '1'),
	('liver', 'assets.pocket.health/rr_DclY5zgU', '1'),
	('lungs', 'assets.pocket.health/rr_dKqZ4W52', '1'),
	('lymph node', 'assets.pocket.health/rr_iupC-yKf', '1'),
	('mri', 'assets.pocket.health/rr_CAfOqdZo', '1'),
	('pancreas', 'assets.pocket.health/rr_19-epSDG', '1'),
	('paranasal sinuses', 'assets.pocket.health/rr_QfLaEtNy', '1'),
	('pelvis', 'assets.pocket.health/rr_I4VJrgom', '1'),
	('pneumothorax', 'assets.pocket.health/rr_w4M8W51I', '1'),
	('rectum', 'assets.pocket.health/rr_tKa6C_JZ', '1'),
	('skull', 'assets.pocket.health/rr_e3SKmZ0t', '1'),
	('spleen', 'assets.pocket.health/rr_cRAkUcxa', '1'),
	('urinary tract', 'assets.pocket.health/rr_vb9GxOxv', '1'),
	('uterus', 'assets.pocket.health/rr_r2iVf0w2', '1'),
	('vein', 'assets.pocket.health/rr_tDnWFocb', '1');
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE term_definitions td 
	JOIN temp_image_info tii on td.term = tii.term
	SET td.image_link = tii.image_link, td.citation_id = tii.citation_id
-- +goose StatementEnd


-- +goose Down
-- +goose StatementBegin
UPDATE term_definitions SET image_link = NULL, citation_id = NULL;
-- +goose StatementEnd
