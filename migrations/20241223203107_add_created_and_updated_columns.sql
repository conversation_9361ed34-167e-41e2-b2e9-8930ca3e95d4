-- +goose Up

-- Deletes Row added during testing Goose Migrations behaviour
-- +goose StatementBegin
DELETE FROM term_definitions WHERE term = "fake_value_2";
-- +goose StatementEnd

-- +goose StatementBegin
ALTER TABLE term_definitions
ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
-- +goose StatementEnd

-- Explicitly ignore the default value for existing rows, as we want to understand at an immediate glance which rows
-- -- were created prior to this statement
-- +goose StatementBegin
UPDATE term_definitions SET created_at = NULL, updated_at = NULL;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE term_definitions
DROP COLUMN created_at,
DROP COLUMN updated_at;
-- +goose StatementEnd