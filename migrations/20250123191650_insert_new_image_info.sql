-- +goose Up

-- +goose StatementBegin
-- Need to add this definition to the term in QA only as it is currently blank. Not putting a down migration for this.
UPDATE term_definitions
set
    def = "An organ in the pelvis that holds urine."
where
    term = "bladder"
    and def = "";
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TEMPORARY TABLE temp_image_info (
    term varchar(255),
    image_link varchar(255),
    citation_id int(11)
);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO
    temp_image_info (term, image_link, citation_id)
VALUES (
        'Adrenals',
        'assets.pocket.health/rr_vWbo8FqS',
        '3'
    ),
    (
        'Appendix',
        'assets.pocket.health/rr_vqrhAMX2',
        '3'
    ),
    (
        'Bladder',
        'assets.pocket.health/rr_AojT7wYl',
        '3'
    ),
    (
        'Bowel',
        'assets.pocket.health/rr_Md-Xrwiu',
        '3'
    ),
    (
        'Descending Colon',
        'assets.pocket.health/rr_onGa797I',
        '3'
    ),
    (
        'Duodenum',
        'assets.pocket.health/rr_SI8kw_-I',
        '3'
    ),
    (
        'Esoph',
        'assets.pocket.health/rr_DoK_ywak',
        '3'
    ),
    (
        'Esophagus',
        'assets.pocket.health/rr_kQdXu2RF',
        '3'
    ),
    (
        'GI Tract',
        'assets.pocket.health/rr_0lIXylde',
        '3'
    ),
    (
        'GI',
        'assets.pocket.health/rr_CoOcswHK',
        '3'
    ),
    (
        'Gastrointestinal Tract',
        'assets.pocket.health/rr_fn3pJIwZ',
        '3'
    ),
    (
        'Kidney',
        'assets.pocket.health/rr_JAaKY26n',
        '3'
    ),
    (
        'Kidneys',
        'assets.pocket.health/rr_qyyP81-q',
        '3'
    ),
    (
        'Large Intestine',
        'assets.pocket.health/rr_tRbAD7Hq',
        '3'
    ),
    (
        'Rectal',
        'assets.pocket.health/rr_fJc-QwNI',
        '3'
    ),
    (
        'Rectum',
        'assets.pocket.health/rr_tKa6C_JZ',
        '3'
    ),
    (
        'Renal artery',
        'assets.pocket.health/rr_qk50ApIn',
        '3'
    ),
    (
        'Renal',
        'assets.pocket.health/rr_9mnzQovD',
        '3'
    ),
    (
        'Sigmoid Colon',
        'assets.pocket.health/rr_y-qUdgRY',
        '3'
    ),
    (
        'Small Bowel',
        'assets.pocket.health/rr_-b7bvUGp',
        '3'
    ),
    (
        'Small Intestine',
        'assets.pocket.health/rr_o6fNxqR6',
        '3'
    ),
    (
        'Ureter',
        'assets.pocket.health/rr_hmF3bP7e',
        '3'
    ),
    (
        'Urinary Tract',
        'assets.pocket.health/rr_vb9GxOxv',
        '3'
    ),
    (
        'Urinary',
        'assets.pocket.health/rr_PfrHa6fr',
        '3'
    );
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE term_definitions td
JOIN temp_image_info tii on td.term = tii.term
SET
    td.image_link = tii.image_link,
    td.citation_id = tii.citation_id;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = NULL,
    citation_id = NULL
WHERE
    term IN (
        'adrenals',
        'appendix',
        'bladder',
        'bowel',
        'descending colon',
        'duodenum',
        'esoph',
        'esophagus',
        'gi tract',
        'gi',
        'gastrointestinal tract',
        'kidney',
        'kidneys',
        'large intestine',
        'rectal',
        'rectum',
        'renal artery',
        'renal',
        'sigmoid colon',
        'small bowel',
        'small intestine',
        'ureter',
        'urinary tract',
        'urinary'
    );
-- +goose StatementEnd