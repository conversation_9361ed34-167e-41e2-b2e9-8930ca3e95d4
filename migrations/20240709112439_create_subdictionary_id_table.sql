-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS subdictionaries (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO `subdictionaries` (name)
VALUES 
('breast');
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS `subdictionaries`;
-- +goose StatementEnd
