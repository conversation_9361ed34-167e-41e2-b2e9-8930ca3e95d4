-- +goose Up
-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = NULL,
    citation_id = NULL
WHERE
    term IN (
        'duodenum',
        'fat',
        'gastrointestinal tract'
    );
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TEMPORARY TABLE temp_image_info (
    term varchar(255),
    image_link varchar(255),
    citation_id int(11)
);
-- +goose StatementEnd
-- +goose StatementBegin
INSERT INTO
    temp_image_info (term, image_link, citation_id)
VALUES (
        'Adrenal',
        'assets.pocket.health/rr_Vi2t6Fqm',
        '3'
    ),
    (
        'Bile',
        'assets.pocket.health/rr_-7y2TWKZ',
        '3'
    ),
    (
        'Biliary Ducts',
        'assets.pocket.health/rr_lw9Spfgo',
        '3'
    ),
    (
        'Biliary',
        'assets.pocket.health/rr_ujo7ga5u',
        '3'
    ),
    (
        'Esophagus',
        'assets.pocket.health/rr_kQdXu2RF',
        '3'
    ),
    (
        'Gallbladder',
        'assets.pocket.health/rr_Z_Lw8m2I',
        '3'
    ),
    (
        'Hepatic veins',
        'assets.pocket.health/rr_4NGK-TzL',
        '3'
    ),
    (
        'Hepatic',
        'assets.pocket.health/rr_-G_3elgl',
        '3'
    ),
    (
        'Kidney',
        'assets.pocket.health/rr_JAaKY26n',
        '3'
    ),
    (
        'Kidneys',
        'assets.pocket.health/rr_qyyP81-q',
        '3'
    ),
    (
        'Liver',
        'assets.pocket.health/rr_DclY5zgU',
        '3'
    ),
    (
        'Pancreas',
        'assets.pocket.health/rr_19-epSDG',
        '3'
    ),
    (
        'Portal vein',
        'assets.pocket.health/rr_4tnQKJ2o',
        '3'
    ),
    (
        'Renal artery',
        'assets.pocket.health/rr_qk50ApIn',
        '3'
    ),
    (
        'Renal',
        'assets.pocket.health/rr_9mnzQovD',
        '3'
    ),
    (
        'Spleen',
        'assets.pocket.health/rr_cWawUxlz',
        '3'
    ),
    (
        'Splenic vein',
        'assets.pocket.health/rr_pm7MRJfj',
        '3'
    ),
    (
        'Stomach',
        'assets.pocket.health/rr_rbHBLoY1',
        '3'
    ),
    (
        'Ureter',
        'assets.pocket.health/rr_hmF3bP7e',
        '3'
    );
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE term_definitions td
JOIN temp_image_info tii on td.term = tii.term
SET
    td.image_link = tii.image_link,
    td.citation_id = tii.citation_id;
-- +goose StatementEnd

-- +goose Down

-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = NULL,
    citation_id = NULL
WHERE
    term IN (
        'adrenal',
        'bile',
        'biliary ducts',
        'biliary',
        'esophagus',
        'gallbladder',
        'hepatic veins',
        'hepatic',
        'kidney',
        'kidneys',
        'liver',
        'pancreas',
        'portal vein',
        'renal artery',
        'renal',
        'spleen',
        'splenic vein',
        'stomach',
        'ureter',
    );
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = 'assets.pocket.health/rr_TSsJpcs1',
    citation_id = "3"
WHERE
    term = "duodenum";
-- +goose StatementEnd
-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = 'assets.pocket.health/rr_NH2wwXGS',
    citation_id = "1"
WHERE
    term = "fat";
-- +goose StatementEnd
-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = 'assets.pocket.health/rr_WnAQ64ms',
    citation_id = "3"
WHERE
    term = "gastrointestinal tract";
-- +goose StatementEnd