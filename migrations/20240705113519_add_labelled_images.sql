-- +goose Up
-- +goose StatementBegin
CREATE TEMPORARY TABLE IF NOT EXISTS temp_image_info(
		term varchar(255),
		image_link varchar(255),
		citation_id int(11)
		)
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO temp_image_info (term, image_link, citation_id) VALUES 
    ('arteries', 'assets.pocket.health/rr_Nf7xE88y', '2'), 
    ('gallbladder', 'assets.pocket.health/rr_Z_Lw8m2I', '2'), 
    ('heart', 'assets.pocket.health/rr_RkAat9Hd', '2'), 
    ('hiatal hernia', 'assets.pocket.health/rr_l5PrSuNR', '2'), 
    ('vein', 'assets.pocket.health/rr_tDnWFocb', '2');
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TEMPORARY TABLE backup_term_definitions AS
    SELECT term, image_link, citation_id
    FROM term_definitions
    WHERE term IN (SELECT term FROM temp_image_info);
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE term_definitions td 
	JOIN temp_image_info tii on td.term = tii.term
	SET td.image_link = tii.image_link, td.citation_id = tii.citation_id
-- +goose StatementEnd


-- +goose Down
-- +goose StatementBegin
UPDATE term_definitions td 
	JOIN backup_term_definitions btd on td.term = btd.term
	SET td.image_link = btd.image_link, td.citation_id = btd.citation_id
-- +goose StatementEnd
