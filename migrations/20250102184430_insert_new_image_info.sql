-- +goose Up
-- +goose StatementBegin
INSERT INTO
    term_definitions (term, def)
VALUES (
        'fibroglandular tissue',
        'A normal type of body tissue in the breast. It contains "fibrous" tissue which holds different structures in place, and "glandular" tissue which helps produce breast milk.'
    );
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = NULL,
    citation_id = NULL
WHERE
    term IN (
        'aortic aneurysm',
        'fetus',
        'foot',
        'skull',
        'vein'
    );
-- +goose StatementEnd

-- +goose StatementBegin
DELETE FROM term_definitions WHERE term IN ('breast density');
-- +goose StatementEnd

-- +goose StatementBegin
CREATE TEMPORARY TABLE temp_image_info (
    term varchar(255),
    image_link varchar(255),
    citation_id int(11)
);
-- +goose StatementEnd

-- +goose StatementBegin
INSERT INTO
    temp_image_info (term, image_link, citation_id)
VALUES (
        'Bile duct',
        'assets.pocket.health/rr_BOmLZVqU',
        '3'
    ),
    (
        'Bile ducts',
        'assets.pocket.health/rr_VZGHPmNr',
        '3'
    ),
    (
        'Bile',
        'assets.pocket.health/rr_-7y2TWKZ',
        '3'
    ),
    (
        'Biliary ducts',
        'assets.pocket.health/rr_lw9Spfgo',
        '3'
    ),
    (
        'Biliary system',
        'assets.pocket.health/rr_m0msL-QG',
        '3'
    ),
    (
        'Biliary tree',
        'assets.pocket.health/rr_23Cx8dlu',
        '3'
    ),
    (
        'Biliary',
        'assets.pocket.health/rr_ujo7ga5u',
        '3'
    ),
    (
        'Breast',
        'assets.pocket.health/rr_EWPsxhmi',
        '3'
    ),
    (
        'Breasts',
        'assets.pocket.health/rr_fX-yF4uX',
        '3'
    ),
    (
        'Category A',
        'assets.pocket.health/rr_UwZdHkeq',
        '3'
    ),
    (
        'Category B',
        'assets.pocket.health/rr_QVItc-mW',
        '3'
    ),
    (
        'Category C',
        'assets.pocket.health/rr_6gbSNiS8',
        '3'
    ),
    (
        'Category D',
        'assets.pocket.health/rr_qDNTarfR',
        '3'
    ),
    (
        'Entirely fatty',
        'assets.pocket.health/rr_8o8ruIH7',
        '3'
    ),
    (
        'Extra Axial Fluid Collection',
        'assets.pocket.health/rr_yPNKS6Qw',
        '3'
    ),
    (
        'Extra Axial Fluid Collections',
        'assets.pocket.health/rr_0g10-aEO',
        '3'
    ),
    (
        'Extra-Axial Collections',
        'assets.pocket.health/rr_St9oFeJT',
        '3'
    ),
    (
        'Extra-Axial Fluid Collection',
        'assets.pocket.health/rr_CM7Rqes5',
        '3'
    ),
    (
        'Extra-Axial Fluid Collections',
        'assets.pocket.health/rr_pBm92fmX',
        '3'
    ),
    (
        'Fibroglandular tissue',
        'assets.pocket.health/rr_pqI1c-zD',
        '3'
    ),
    (
        'Fibrous tissue',
        'assets.pocket.health/rr_nZBMiDMW',
        '3'
    ),
    (
        'Glandular tissue',
        'assets.pocket.health/rr_1zWnzfJ8',
        '3'
    ),
    (
        'Maxillary Sinus',
        'assets.pocket.health/rr_MaVuvxaV',
        '3'
    ),
    (
        'Midline Shift',
        'assets.pocket.health/rr_N5vPr9y6',
        '3'
    ),
    (
        'Paranasal Sinuses',
        'assets.pocket.health/rr_QfLaEtNy',
        '3'
    ),
    (
        'Sphenoid Sinus',
        'assets.pocket.health/rr_o7ouCvQm',
        '3'
    );
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE term_definitions td
JOIN temp_image_info tii on td.term = tii.term
SET
    td.image_link = tii.image_link,
    td.citation_id = tii.citation_id;
-- +goose StatementEnd

-- +goose Down

UPDATE term_definitions
SET
    image_link = NULL,
    citation_id = NULL
WHERE
    term IN (
        'bile duct',
        'bile ducts',
        'bile',
        'biliary ducts',
        'biliary system',
        'biliary tree',
        'biliary',
        'breast',
        'breasts',
        'category a',
        'category b',
        'category c',
        'category d',
        'entirely fatty',
        'extra axial fluid collection',
        'extra axial fluid collections',
        'extra-axial collections',
        'extra-axial fluid collection',
        'extra-axial fluid collections',
        'fibroglandular tissue',
        'fibrous tissue',
        'glandular tissue',
        'maxillary sinus',
        'midline shift',
        'paranasal sinuses',
        'sphenoid sinus'
    );

-- +goose StatementBegin
INSERT INTO
    term_definitions (term, def, subdictionary_id)
VALUES (
        'breast density',
        'Breast density refers to the proportion of fibrous and glandular tissue compared to fatty tissue in the breast. Dense breast tissue is common, but it can make detecting abnormalities more challenging during imaging.',
        '1'
    );
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = 'assets.pocket.health/rr_k7pCJa1O',
    citation_id = "1"
WHERE
    term = "aortic aneurysm";
-- +goose StatementEnd
-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = 'assets.pocket.health/rr_gqm8d3FU',
    citation_id = "1"
WHERE
    term = "fetus";
-- +goose StatementEnd
-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = 'assets.pocket.health/rr_8Zt9LO1I',
    citation_id = "1"
WHERE
    term = "foot";
-- +goose StatementEnd
-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = 'assets.pocket.health/rr_e3SKmZ0t',
    citation_id = "1"
WHERE
    term = "skull";
-- +goose StatementEnd
-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = 'assets.pocket.health/rr_tDnWFocb',
    citation_id = "2"
WHERE
    term = "vein";
-- +goose StatementEnd

-- +goose StatementBegin
DELETE FROM term_definitions WHERE term IN ('fibroglandular tissue');
-- +goose StatementEnd