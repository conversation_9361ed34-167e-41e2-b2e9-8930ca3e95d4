-- +goose Up

-- +goose StatementBegin
CREATE TEMPORARY TABLE temp_image_info (
    term varchar(255),
    image_link varchar(255),
    citation_id int(11)
)
-- +goose StatementEnd
-- +goose StatementBegin
INSERT INTO
    temp_image_info (term, image_link, citation_id)
VALUES (
        'AP Diameter',
        'assets.pocket.health/rr_H7lVj3sq',
        '3'
    ),
    (
        'AP',
        'assets.pocket.health/rr_RqQDDVvW',
        '3'
    ),
    (
        'Anterior',
        'assets.pocket.health/rr__8dknEgu',
        '3'
    ),
    (
        'Axial Plane',
        'assets.pocket.health/rr_AeK9cF0Y',
        '3'
    ),
    (
        'Axial',
        'assets.pocket.health/rr_AIFeKjFo',
        '3'
    ),
    (
        'Biliary Ducts',
        'assets.pocket.health/rr_lw9Spfgo',
        '3'
    ),
    (
        'Biliary',
        'assets.pocket.health/rr_ujo7ga5u',
        '3'
    ),
    (
        'Contralateral',
        'assets.pocket.health/rr_8y740M3N',
        '3'
    ),
    (
        'Coronal Plane',
        'assets.pocket.health/rr_CZ4RjTfv',
        '3'
    ),
    (
        'Coronal',
        'assets.pocket.health/rr_EylamdGm',
        '3'
    ),
    (
        'Distal',
        'assets.pocket.health/rr_xwncJRIf',
        '3'
    ),
    (
        'Duodenum',
        'assets.pocket.health/rr_TSsJpcs1',
        '3'
    ),
    (
        'Esophagus',
        'assets.pocket.health/rr_kQdXu2RF',
        '3'
    ),
    (
        'Gallbladder',
        'assets.pocket.health/rr_Z_Lw8m2I',
        '3'
    ),
    (
        'Gastrointestinal Tract',
        'assets.pocket.health/rr_WnAQ64ms',
        '3'
    ),
    (
        'Hepatic Veins',
        'assets.pocket.health/rr_4NGK-TzL',
        '3'
    ),
    (
        'Hepatic',
        'assets.pocket.health/rr_-G_3elgl',
        '3'
    ),
    (
        'Inferior',
        'assets.pocket.health/rr_ax8D0shC',
        '3'
    ),
    (
        'Ipsilateral',
        'assets.pocket.health/rr_Jd3ysAlj',
        '3'
    ),
    (
        'Lateral',
        'assets.pocket.health/rr_jpqY-rtK',
        '3'
    ),
    (
        'Liver',
        'assets.pocket.health/rr_DclY5zgU',
        '3'
    ),
    (
        'Medial',
        'assets.pocket.health/rr_nh_Ag11P',
        '3'
    ),
    (
        'Portal Vein',
        'assets.pocket.health/rr_4tnQKJ2o',
        '3'
    ),
    (
        'Posterior',
        'assets.pocket.health/rr_6gH_qurD',
        '3'
    ),
    (
        'Proximal',
        'assets.pocket.health/rr_2r6jbAvr',
        '3'
    ),
    (
        'Quadrant',
        'assets.pocket.health/rr_jifrwPBW',
        '3'
    ),
    (
        'Sagittal Plane',
        'assets.pocket.health/rr_M0GStVvk',
        '3'
    ),
    (
        'Sagittal',
        'assets.pocket.health/rr_oPWQa26M',
        '3'
    ),
    (
        'Spleen',
        'assets.pocket.health/rr_cWawUxlz',
        '3'
    ),
    (
        'Splenic Vein',
        'assets.pocket.health/rr_pm7MRJfj',
        '3'
    ),
    (
        'Stomach',
        'assets.pocket.health/rr_rbHBLoY1',
        '3'
    ),
    (
        'Superior',
        'assets.pocket.health/rr_HamL14Q9',
        '3'
    ),
    (
        'Transverse Plane',
        'assets.pocket.health/rr_NlC2Aiu2',
        '3'
    ),
    (
        'Transverse',
        'assets.pocket.health/rr_RZfyq0Pm',
        '3'
    );
-- +goose StatementEnd

-- +goose StatementBegin
UPDATE term_definitions td
JOIN temp_image_info tii on td.term = tii.term
SET
    td.image_link = tii.image_link,
    td.citation_id = tii.citation_id;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
UPDATE term_definitions
SET
    image_link = NULL,
    citation_id = NULL
WHERE
    term IN (
        'anterior',
        'ap diameter',
        'ap',
        'axial plane',
        'axial',
        'biliary ducts',
        'biliary',
        'contralateral',
        'coronal plane',
        'coronal',
        'distal',
        'duodenum',
        'esophagus',
        'gallbladder',
        'gastrointestinal tract',
        'hepatic veins',
        'hepatic',
        'inferior',
        'ipsilateral',
        'lateral',
        'liver',
        'medial',
        'portal vein',
        'posterior',
        'proximal',
        'quadrant',
        'sagittal plane',
        'sagittal',
        'spleen',
        'splenic vein',
        'stomach',
        'superior',
        'transverse',
        'transverse plane'
    );
-- +goose StatementEnd