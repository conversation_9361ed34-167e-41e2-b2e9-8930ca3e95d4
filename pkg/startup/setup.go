package startup

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/sirupsen/logrus"
	"golang.org/x/oauth2"

	_ "github.com/go-sql-driver/mysql"
	"gitlab.com/pockethealth/phutils/v10/pkg/auth/service"
	phconfig "gitlab.com/pockethealth/phutils/v10/pkg/config"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	phkeyvault "gitlab.com/pockethealth/phutils/v10/pkg/keyvault"
	"gitlab.com/pockethealth/reportprocessor/pkg/blob"
	"gitlab.com/pockethealth/reportprocessor/pkg/insights"
	"gitlab.com/pockethealth/reportprocessor/pkg/insights/insightssvc"
	"gitlab.com/pockethealth/reportprocessor/pkg/topicsub"
)

// Config contains config values for the HTTP Server and Services/Servicers
type Config struct {
	ShutdownTimeSeconds int

	AllowedOrigins []string
	AllowedMethods []string
	AllowedHeaders []string
	ExposedHeaders []string
}

// Dependencies holds dependencies needed to start up the HTTP Server and Services/Servicers
type Dependencies struct {
	MySQLDB           *sql.DB
	Keyvault          *phkeyvault.Keyvault
	Blobstore         blob.Client
	InsightsBlobstore blob.Client
	InsightsClient    *insightssvc.InsightsClient
	TopicSuscriptions []topicsub.TopicSubscription
}

// Wire up dependencies and load config using config file and, if needed, keyvault
func Setup(cfg map[string]json.RawMessage) (*Config, *Dependencies) {
	// configure logger
	if lvl, ok := cfg["log_level"]; ok {
		var lvlString string
		err := json.Unmarshal(lvl, &lvlString)
		if err == nil {
			loglevel, err2 := logrus.ParseLevel(lvlString)
			if err2 == nil {
				logrus.SetLevel(loglevel)
			}
		}
	}
	textLogFormat, err := cfg["text_log_format"].MarshalJSON()
	if err == nil && string(textLogFormat) == "true" {
		logrus.SetFormatter(&logrus.TextFormatter{FullTimestamp: true})
	} else {
		logrus.SetFormatter(&logrus.JSONFormatter{})
	}

	// setup keyvault so we can load config values from it
	var keyvaultName string
	if err := json.Unmarshal(cfg["keyvault_name"], &keyvaultName); err != nil {
		logrus.Info("Couldn't load keyvault name")
		keyvaultName = ""
	}

	kv, err := phkeyvault.NewKeyvault(keyvaultName)
	if err != nil {
		logrus.Infof(
			"Unable to create Keyvault client: %s.  You will not be able to use keyvault secret names in configuration.\n",
			err,
		)
	}

	bs, err := setupBlobStore(cfg, kv)
	if err != nil {
		logrus.WithError(err).Fatal("unable to setup blob store")
	}

	ibs, err := setupInsightsBlobStore(cfg, kv)
	if err != nil {
		logrus.WithError(err).Fatal("unable to setup blob store")
	}

	insvc := setupInsightServiceS2S(context.Background(), kv, cfg)

	subs := setupSubscriptions(kv, cfg, bs, ibs, insvc)

	return setupConfig(cfg), &Dependencies{
		MySQLDB:           setupMySQL(cfg, kv),
		Keyvault:          kv,
		Blobstore:         bs,
		InsightsBlobstore: ibs,
		InsightsClient:    insvc,
		TopicSuscriptions: subs,
	}
}

func setupMySQL(cfg map[string]json.RawMessage, kv *phkeyvault.Keyvault) *sql.DB {
	sqlConnStr := phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"mysql_connection_string",
		"mysql_connection_string_sec_name",
	)
	db, err := sql.Open("mysql", sqlConnStr)
	if err != nil {
		logrus.WithError(err).Fatal("unable to perform sql.Open")
	}
	err = db.Ping()
	if err != nil {
		logrus.WithError(err).Fatal("unable to ping db")
	}
	maxIdleConns := 20
	maxOpenConns := 70
	maxIdle, err := cfg["max_idle_sql_conn"].MarshalJSON()
	if err != nil {
		logrus.WithField("default", maxIdleConns).
			WithError(err).
			Error("could not read max idle sql conn config, using default")
	} else {
		maxIdleConfig, err := strconv.Atoi(string(maxIdle))
		if err == nil {
			maxIdleConns = maxIdleConfig
		}
	}
	maxOpen, err := cfg["max_open_sql_conn"].MarshalJSON()
	if err != nil {
		logrus.WithField("default", maxOpenConns).
			WithError(err).
			Error("could not read max open sql conn config, using default")
	} else {
		maxOpenConfig, err := strconv.Atoi(string(maxOpen))
		if err == nil {
			maxOpenConns = maxOpenConfig
		}
	}
	db.SetMaxIdleConns(maxIdleConns)
	db.SetMaxOpenConns(maxOpenConns)
	db.SetConnMaxLifetime(time.Minute * 5)
	return db
}

func setupBlobStore(cfg map[string]json.RawMessage, kv *phkeyvault.Keyvault) (blob.Client, error) {
	accountName := phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"az_storage_account",
		"az_storage_account_sec_name",
	)
	containerName := phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"az_container",
		"az_container_sec_name",
	)

	return blob.NewBlobStoreClient(accountName, containerName)
}

func setupInsightsBlobStore(
	cfg map[string]json.RawMessage,
	kv *phkeyvault.Keyvault,
) (blob.Client, error) {
	accountName := phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"insights_storage_account",
		"insights_storage_account_sec_name",
	)
	containerName := phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"insights_container",
		"insights_container_sec_name",
	)

	return blob.NewBlobStoreClient(accountName, containerName)
}

func setupConfig(cfg map[string]json.RawMessage) *Config {
	return &Config{
		AllowedOrigins: marshalJsonStringArray(cfg, "allowed_origins"),
		AllowedMethods: []string{"GET", "OPTIONS"},
		AllowedHeaders: []string{
			"Accept",
			"Authorization",
			"Enctype",
			"Content-type",
		},
		ExposedHeaders: []string{
			"Content-disposition",
			"cache-control",
			"content-length",
			"expires",
			"pragma",
		},
	}
}

func setupInsightServiceS2S(
	ctx context.Context,
	kv *phkeyvault.Keyvault,
	cfg map[string]json.RawMessage,
) *insightssvc.InsightsClient {
	insightsURL := phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"insights_client_url",
		"",
	)

	insightsBasicAuthPw := phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"",
		"insights_basic_auth_pw",
	)

	authUrl := phconfig.ValFromConfOrKeyvault(
		kv, cfg, "gateway_auth_url", "")

	tokenSrc := service.NewTokenSource("/var/run/secrets/kubernetes.io/serviceaccount/token",
		authUrl, "reportinsights", ctx)

	// Create base http client with oauth transport
	baseClient := &http.Client{
		Timeout: 15 * time.Second,
		Transport: &oauth2.Transport{
			Source: oauth2.ReuseTokenSource(nil, tokenSrc),
			Base:   http.DefaultTransport,
		},
	}
	retryParams := &httpclient.RetryParameters{
		MaxNumRetries: 3,
		StartBase:     1.0,
		BaseDuration:  10 * time.Second, // max wait time
	}
	client := httpclient.NewHTTPClient(baseClient, retryParams)
	return insightssvc.S2SNewClient(insightsURL, insightsBasicAuthPw, client)
}

func setupSubscriptions(
	kv *phkeyvault.Keyvault,
	cfg map[string]json.RawMessage,
	objsBlob blob.Client,
	insightsBlob blob.Client,
	insightsClient *insightssvc.InsightsClient,
) []topicsub.TopicSubscription {
	// TODO: if more topics/subscriptions needed, refactor into a more reusable config/setup function
	sbConnStr := phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"",
		"service_bus_conn_str_sec_name",
	)
	topic := phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"topic_name",
		"",
	)
	subscription := phconfig.ValFromConfOrKeyvault(
		kv,
		cfg,
		"subscription",
		"",
	)
	pollFreq := 1
	pollFreqStr, err := cfg["poll_frequency_seconds"].MarshalJSON()
	if err != nil {
		logrus.WithField("default", pollFreq).
			WithError(err).
			Error("could not read poll_frequency_seconds config, using default")
	} else {
		pollFreqCfg, err := strconv.Atoi(string(pollFreqStr))
		if err == nil {
			pollFreq = pollFreqCfg
		}
	}

	sub := topicsub.NewAZTopicSubscription(
		sbConnStr,
		topic,
		subscription,
		pollFreq,
		topicsub.HandleTransferEvent,
		insights.InsightsJobDeps{
			ObjsBlob:     objsBlob,
			InsightsBlob: insightsBlob,
			InsightsSvc:  insightsClient,
		},
	)
	if sub == nil {
		logrus.WithFields(logrus.Fields{
			"topic":        topic,
			"subscription": subscription,
		}).Fatalf("could not setup new subscription")
	}

	return []topicsub.TopicSubscription{sub}
}
