package topicsub

import (
	"context"
	"errors"
	"testing"

	"github.com/Azure/azure-sdk-for-go/sdk/messaging/azservicebus"
)

func TestAZTopicSubscription(t *testing.T) {
	rcvr := &mockReceiver{}
	mockArgs := mockHandleArgs{}

	cleanup := func() {
		rcvr.Clear()
		mockArgs.clear()
	}

	sub := AZTopicSubscription{
		Topic:             "testtopic",
		Subscription:      "testsub",
		HandleMessageFunc: mockHandleFunc,
		HandleArgs:        &mockArgs,
		rcvr:              rcvr,
	}

	t.Run("if error unmarshaling, expect in deadletter", func(t *testing.T) {

		t.Cleanup(cleanup)
		rcvr.queue = []*azservicebus.ReceivedMessage{
			{Body: []byte(`{"event_type" not jsoon`)},
		}
		sub.processMessages()
		if len(rcvr.dlq) != 1 {
			t.Fatalf("expected 1 deadlettered message, got %d", len(rcvr.dlq))
		}

		if mockArgs.count != 0 {
			t.Fatalf("expected zero messages to be handled, got %d", mockArgs.count)
		}
	})

	t.Run("if unmarshaled successfully, expect in completed", func(t *testing.T) {

		t.Cleanup(cleanup)
		rcvr.queue = []*azservicebus.ReceivedMessage{
			{Body: []byte(`{"event_type":"transfer_challenge", "report_ids":["areport"]}`)},
			{Body: []byte(`{"event_type":"transfer_challenge", "report_ids":["areport"]}`)},
		}
		sub.processMessages()

		if len(rcvr.completed) != 2 {
			t.Fatalf("expected 2 completed messages, got %d", len(rcvr.completed))
		}

		if mockArgs.count != 2 {
			t.Fatalf("expected 2 messages to be handled, got %d", mockArgs.count)
		}
	})

	t.Run("if message complete action is unsuccessful, do not process", func(t *testing.T) {

		t.Cleanup(cleanup)
		rcvr.completeErr = errors.New("complete fails")

		rcvr.queue = []*azservicebus.ReceivedMessage{
			{Body: []byte(`{"event_type":"transfer_challenge", "report_ids":["areport"]}`)},
			{Body: []byte(`{"event_type":"transfer_challenge", "report_ids":["areport"]}`)},
		}
		sub.processMessages()

		if mockArgs.count != 0 {
			t.Fatalf("expected 0 messages to be handled, got %d", mockArgs.count)
		}

		if len(rcvr.completed) != 2 {
			t.Fatalf("expected 2 messages to be attempted to be completed, got %d", len(rcvr.completed))
		}
	})

}

type mockHandleArgs struct {
	count int
}

func (m *mockHandleArgs) clear() {
	m.count = 0
}

func mockHandleFunc(ctx context.Context, evt Event, args interface{}) {
	mockArgs, ok := args.(*mockHandleArgs)
	if ok {
		mockArgs.count = mockArgs.count + 1
	}
}
