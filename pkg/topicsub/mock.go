package topicsub

import (
	"context"

	"github.com/Azure/azure-sdk-for-go/sdk/messaging/azservicebus"
	"github.com/sirupsen/logrus"
)

type mockReceiver struct {
	queue       []*azservicebus.ReceivedMessage
	dlq         []*azservicebus.ReceivedMessage
	completed   []*azservicebus.ReceivedMessage
	completeErr error
}

func (m *mockReceiver) ReceiveMessages(ctx context.Context, maxMessages int, options *azservicebus.ReceiveMessagesOptions) ([]*azservicebus.ReceivedMessage, error) {
	msgs := m.queue
	m.queue = []*azservicebus.ReceivedMessage{}
	return msgs, nil
}

func (m *mockReceiver) DeadLetterMessage(ctx context.Context, message *azservicebus.ReceivedMessage, options *azservicebus.DeadLetterOptions) error {
	logrus.Info("deadletter")
	m.dlq = append(m.dlq, message)
	return nil
}

func (m *mockReceiver) CompleteMessage(ctx context.Context, message *azservicebus.ReceivedMessage, options *azservicebus.CompleteMessageOptions) error {
	m.completed = append(m.completed, message)
	return m.completeErr
}

func (m *mockReceiver) Clear() {
	m.queue = []*azservicebus.ReceivedMessage{}
	m.dlq = []*azservicebus.ReceivedMessage{}
	m.completed = []*azservicebus.ReceivedMessage{}
	m.completeErr = nil
}
