package topicsub

import (
	"context"

	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/reportprocessor/pkg/insights"
)

const (
	TRANSFER_CHALLENGE_EVENT_TYPE = "transfer_challenge"
	TRANSFER_FINALIZED_EVENT_TYPE = "transfer_finalized"
)

type TransferEvent struct {
	TransferId       string              `json:"transfer_id"`
	AccountId        string              `json:"account_id"`
	ProviderId       int64               `json:"provider_id"`
	ReportIds        []string            `json:"report_ids"`
	Exams            map[string][]string `json:"exams"`
	Region           string              `json:"region"`
	EventType        string              `json:"event_type"`
	TimestampSeconds int64               `json:"timestamp_seconds"`
}

func (t *TransferEvent) IsDefault() bool {
	// can't compare to empty event bc []string cannot be compared like that
	return t.TransferId == "" &&
		t.AccountId == "" &&
		t.ProviderId == 0 &&
		t.Region == "" &&
		t.EventType == "" &&
		t.TimestampSeconds == 0
}

func (t *TransferEvent) ApplicationProperties() map[string]any {
	return map[string]any{
		"region":     t.Region,
		"event_type": t.EventType,
	}
}

func HandleTransferEvent(ctx context.Context, evt Event, args interface{}) {
	// lint:ignore SA1029
	ctx = context.WithValue(ctx, "transfer_event", evt)
	ctx = context.WithValue(ctx, "args", args)
	logger := logutils.CtxLogger(ctx)
	logger.Debug("received transfer event")

	txEvt, ok := evt.(*TransferEvent)
	if !ok {
		logger.Error("unexpected transfer event format")
		return
	}

	insightDeps, ok := args.(insights.InsightsJobDeps)
	if !ok {
		logger.Error("invalid args passed to HandleTransferEvent")
		return
	}

	if txEvt.EventType == TRANSFER_CHALLENGE_EVENT_TYPE {
		handleTransferChallenge(ctx, txEvt, insightDeps)
        }
}

func handleTransferChallenge(
	ctx context.Context,
	evt *TransferEvent,
	insightDeps insights.InsightsJobDeps,
) {
	for _, rId := range evt.ReportIds {
		// preprocess reports before user accesses
		insights.PrepareInsights(ctx, rId, insightDeps)
	}
}
