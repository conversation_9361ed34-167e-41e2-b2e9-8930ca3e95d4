package topicsub

import (
	"context"
	"encoding/json"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/messaging/azservicebus"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type Event interface {
	IsDefault() bool
	ApplicationProperties() map[string]any
}

type TopicSubscription interface {
	Listen()
	Close()
}

type AZTopicSubscription struct {
	Topic             string
	Subscription      string
	PollFrequencySec  int
	HandleMessageFunc func(context.Context, Event, interface{})
	HandleArgs        interface{}

	rcvr      Receiver
	ticker    *time.Ticker
	closeChan chan bool
}

type Receiver interface {
	ReceiveMessages(ctx context.Context, maxMessages int, options *azservicebus.ReceiveMessagesOptions) ([]*azservicebus.ReceivedMessage, error)
	DeadLetterMessage(ctx context.Context, message *azservicebus.ReceivedMessage, options *azservicebus.DeadLetterOptions) error
	CompleteMessage(ctx context.Context, message *azservicebus.ReceivedMessage, options *azservicebus.CompleteMessageOptions) error
}

func NewAZTopicSubscription(connStr string, topic string, subscription string, pollSec int, handleFunc func(context.Context, Event, interface{}), handleArgs interface{}) TopicSubscription {
	ts := AZTopicSubscription{
		Topic:             topic,
		Subscription:      subscription,
		PollFrequencySec:  pollSec,
		HandleMessageFunc: handleFunc,
		HandleArgs:        handleArgs,
	}
	client, err := azservicebus.NewClientFromConnectionString(connStr, nil)
	if err != nil {
		logrus.WithError(err).Error("could not get new service bus client")
		return nil
	}

	r, err := client.NewReceiverForSubscription(topic, subscription, &azservicebus.ReceiverOptions{ReceiveMode: azservicebus.ReceiveModePeekLock})
	if err != nil {
		logrus.WithError(err).Error("could not get new service bus client")
		return nil
	}
	ts.rcvr = r
	return &ts
}

func (ts *AZTopicSubscription) Listen() {
	if ts.ticker == nil {
		ts.ticker = time.NewTicker(time.Duration(ts.PollFrequencySec) * time.Second)
	}
	ts.closeChan = make(chan bool)
	go func() {
		lg := logrus.WithFields(logrus.Fields{
			"subscription": ts.Subscription,
			"topic":        ts.Topic,
			"freq_s":       ts.PollFrequencySec,
		})
		lg.Info("started az subscription")
		for {
			select {
			case <-ts.closeChan:
				return
			case <-ts.ticker.C:
				ts.processMessages()
			}
		}
	}()
}

func (ts *AZTopicSubscription) processMessages() {
	lg := logrus.WithFields(logrus.Fields{
		"subscription": ts.Subscription,
		"topic":        ts.Topic,
		"freq_s":       ts.PollFrequencySec,
	})
	ctx, cancel := context.WithTimeout(context.TODO(), 300*time.Second)
	defer cancel()

	messages, err := ts.rcvr.ReceiveMessages(ctx,
		5,
		nil,
	)
	if err != nil {
		if err != context.DeadlineExceeded {
			lg.WithError(err).Error("could not receive topic messages")
		}
	}

	for _, message := range messages {
		corID := ""
		if message.CorrelationID != nil {
			corID = *message.CorrelationID
		}
		ctx := context.WithValue(context.TODO(), logutils.CorrelationIdContextKey, corID)

		var evt TransferEvent
		err = json.Unmarshal(message.Body, &evt)
		if err != nil {
			reason := "could not unmarshal message"
			errDesc := err.Error()
			lg.WithError(err).Error(reason)
			errDL := ts.rcvr.DeadLetterMessage(ctx, message, &azservicebus.DeadLetterOptions{ErrorDescription: &errDesc, Reason: &reason})
			if errDL != nil {
				lg.WithError(err).Error("failed to deadletter the message")
			}
			continue
		}

		//not worried too much atm about retrying things yet
		err = ts.rcvr.CompleteMessage(ctx, message, nil)
		if err != nil {
			lg.WithError(err).Error("failed to complete the message")

			//don't process, it's gone back to the queue already
			continue
		}

		ts.HandleMessageFunc(ctx, &evt, ts.HandleArgs)
	}
}

func (ts *AZTopicSubscription) Close() {
	ts.ticker.Stop()
	ts.closeChan <- true
	logrus.WithFields(logrus.Fields{
		"subscription": ts.Subscription,
		"topic":        ts.Topic,
	}).Info("stopped az topic subscription")
}
