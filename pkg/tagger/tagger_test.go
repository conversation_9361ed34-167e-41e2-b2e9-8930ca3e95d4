package tagger

import (
	"context"
	"reflect"
	"testing"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/reportprocessor/pkg/dicomutils"
)

func TestEncapTagger(t *testing.T) {
	tg := Tagger{}
	tg.Build(
		[]string{"grapefruit", "christmas party", "party", "chicken dance", "chicken dance party"},
		[]string{"grapefruit", "chicken dance"},
	)

	cases := []struct {
		name               string
		inputHTML          []byte
		expectedHTML       string
		expectedTermsFound []string
	}{
		{
			"term standalone",
			[]byte(
				`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t">grapefruit</div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			),
			`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t"><span class="phdefinition organvizTerm" keyword="grapefruit">grapefruit</span></div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			[]string{"grapefruit"},
		},
		{
			"term standalone - regardless of letter case",
			[]byte(
				`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t">GRapefruit</div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			),
			`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t"><span class="phdefinition organvizTerm" keyword="GRapefruit">GRapefruit</span></div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			[]string{"GRapefruit"},
		},
		{
			"multiple t divs",
			[]byte(
				`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t">grapefruit</div><div class="t">christmas party</div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			),
			`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t"><span class="phdefinition organvizTerm" keyword="grapefruit">grapefruit</span></div><div class="t"><span class="phdefinition" keyword="christmas party">christmas party</span></div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			[]string{"grapefruit", "christmas party"},
		},
		{
			"nested spans",
			[]byte(
				`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t">grapefruit <span class="ff4">party</span></div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			),
			`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t"><span class="phdefinition organvizTerm" keyword="grapefruit">grapefruit</span> <span class="ff4"><span class="phdefinition" keyword="party">party</span></span></div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			[]string{"party", "grapefruit"},
		},
		{
			"interweaved spans",
			[]byte(
				`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t">gra<span class="_ _0"></span>pefr<span class="_ _1"></span>uit</div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			),
			`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t"><span class="phdefinition organvizTerm" keyword="grapefruit">gra<span class="_ _0"></span>pefr<span class="_ _1"></span>uit</span></div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			[]string{"grapefruit"},
		},
		{
			"multiple pages",
			[]byte(
				`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container">
			<div id="pf1"><div class="pc pc1"><div class="t">grapefruit</div></div></div>
			<div id="pf2"><div class="pc pc2"><div class="t">chicken dance party</div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			),
			`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container">
			<div id="pf1"><div class="pc pc1"><div class="t"><span class="phdefinition organvizTerm" keyword="grapefruit">grapefruit</span></div></div></div>
			<div id="pf2"><div class="pc pc2"><div class="t"><span class="phdefinition" keyword="chicken dance party">chicken dance party</span></div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			[]string{"grapefruit", "chicken dance party"},
		},
		{
			"interweaved spans with more text and multiple phrases",
			[]byte(
				`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t">gr<span class="_ _0"></span>apef<span class="_ _0"></span>ruit i<span class="_ _0"></span>s hav<span class="_ _0"></span>ing a<span class="_ _0"></span> chris<span class="_ _0"></span>tma<span class="_ _0"></span>s party: do not fo<span class="_ _0"></span>rget your chicken dance</div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			),
			`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t"><span class="phdefinition organvizTerm" keyword="grapefruit">gr<span class="_ _0"></span>apef<span class="_ _0"></span>ruit</span> i<span class="_ _0"></span>s hav<span class="_ _0"></span>ing a<span class="_ _0"></span> <span class="phdefinition" keyword="christmas party">chris<span class="_ _0"></span>tma<span class="_ _0"></span>s party</span>: do not fo<span class="_ _0"></span>rget your <span class="phdefinition organvizTerm" keyword="chicken dance">chicken dance</span></div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			[]string{"grapefruit", "christmas party", "chicken dance"},
		},
		{
			"special space characters",
			append(
				append(
					[]byte(
						`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t">chicken`,
					),
					[]byte(string('\u205F'))...),
				[]byte(`dance</div></div></div>
			</div><div class="loading-indicator"></div></body></html>`)...),
			`<!DOCTYPE html><!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) --><html xmlns="http://www.w3.org/1999/xhtml"><head>
			<meta charset="utf-8"/>
			<meta name="generator" content="pdf2htmlEX"/>
			<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<style type="text/css"></style>
			<script></script>
			<script></script>
			<script></script>
			<title></title>
			</head>
			<body>
			<div id="sidebar"><div id="outline"></div></div>
			<div id="page-container"><div id="pf1"><div class="pc pc1"><div class="t"><span class="phdefinition organvizTerm" keyword="chicken dance">chicken` + string(
				'\u205F',
			) + `dance</span></div></div></div>
			</div><div class="loading-indicator"></div></body></html>`,
			[]string{"chicken dance"},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			lg := logrus.WithField("name", c.name)
			out, found, err := tg.TagFns[dicomutils.EncapPDF](context.Background(), lg, c.inputHTML)
			if err != nil {
				t.Fatalf("failed to tag HTML: %v", err)
			}
			if string(out) != c.expectedHTML {
				t.Fatalf("unexpected html output: \n%s\nexpected:\n%s", out, c.expectedHTML)
			}
			if !reflect.DeepEqual(c.expectedTermsFound, found) {
				t.Fatalf("unexpected terms found %v\nexpected: %v", found, c.expectedTermsFound)
			}
		})
	}
}
func TestTagger(t *testing.T) {
	tg := Tagger{}
	tg.Build(
		[]string{"grapefruit", "christmas party", "party", "chicken dance", "chicken dance party"},
		[]string{"grapefruit", "chicken dance"},
	)

	cases := []struct {
		name               string
		inputHTML          []byte
		expectedHTML       string
		expectedTermsFound []string
	}{
		{
			"term standalone",
			[]byte(`<html><head></head><body><div class="t">grapefruit</div></body></html>`),
			`<html><head></head><body><div class="t"><span class="phdefinition organvizTerm" keyword="grapefruit">grapefruit</span></div></body></html>`,
			[]string{"grapefruit"},
		},
		{
			"term end of line",
			[]byte(
				`<html><head></head><body><div class="t">dance is happening, be at the party</div></body></html>`,
			),
			`<html><head></head><body><div class="t">dance is happening, be at the <span class="phdefinition" keyword="party">party</span></div></body></html>`,
			[]string{"party"},
		},
		{
			"term beginning of line",
			[]byte(
				`<html><head></head><body><div class="t">grapefruit dance is happening</div></body></html>`,
			),
			`<html><head></head><body><div class="t"><span class="phdefinition organvizTerm" keyword="grapefruit">grapefruit</span> dance is happening</div></body></html>`,
			[]string{"grapefruit"},
		},
		{
			"two one-word terms end of line",
			[]byte(
				`<html><head></head><body><div class="t">dance is happening, be at the party grapefruit</div></body></html>`,
			),
			`<html><head></head><body><div class="t">dance is happening, be at the <span class="phdefinition" keyword="party">party</span> <span class="phdefinition organvizTerm" keyword="grapefruit">grapefruit</span></div></body></html>`,
			[]string{"party", "grapefruit"},
		},
		{
			"two one-word terms beginning of line",
			[]byte(
				`<html><head></head><body><div class="t">grapefruit party dance is happening</div></body></html>`,
			),
			`<html><head></head><body><div class="t"><span class="phdefinition organvizTerm" keyword="grapefruit">grapefruit</span> <span class="phdefinition" keyword="party">party</span> dance is happening</div></body></html>`,
			[]string{"grapefruit", "party"},
		},
		{
			"multi term with multi spans",
			[]byte(
				`<html><head></head><body><div class="t">Right grapefruit party<span class="ff3">: No christmas party effusion.  Soft tissues are normal.</span></div></body></html>`,
			),
			`<html><head></head><body><div class="t">Right <span class="phdefinition organvizTerm" keyword="grapefruit">grapefruit</span> <span class="phdefinition" keyword="party">party</span><span class="ff3">: No <span class="phdefinition" keyword="christmas party">christmas party</span> effusion.  Soft tissues are normal.</span></div></body></html>`,
			[]string{"grapefruit", "party", "christmas party"},
		},
		{
			"term with punctuation on the end",
			[]byte(
				`<html><head></head><body><div class="t">grapefruit: <span class="ff2">20-Mar-1994</span></div></body></html>`,
			),
			`<html><head></head><body><div class="t"><span class="phdefinition organvizTerm" keyword="grapefruit">grapefruit</span>: <span class="ff2">20-Mar-1994</span></div></body></html>`,
			[]string{"grapefruit"},
		},
		{
			"terms interspersed with other words and punctuation",
			[]byte(
				`<html><head></head><body><div class="t">grapefruit is having a christmas party: do not forget your chicken dance</div></body></html>`,
			),
			`<html><head></head><body><div class="t"><span class="phdefinition organvizTerm" keyword="grapefruit">grapefruit</span> is having a <span class="phdefinition" keyword="christmas party">christmas party</span>: do not forget your <span class="phdefinition organvizTerm" keyword="chicken dance">chicken dance</span></div></body></html>`,
			[]string{"grapefruit", "christmas party", "chicken dance"},
		},
		{
			"term span term",
			[]byte(
				`<html><head></head><body><div class="t">Right grapefruit<span class="ff3">: No christmas party effusion.  Soft tissues are normal.</span> Here is some more party text now.</div></body></html>`,
			),
			`<html><head></head><body><div class="t">Right <span class="phdefinition organvizTerm" keyword="grapefruit">grapefruit</span><span class="ff3">: No <span class="phdefinition" keyword="christmas party">christmas party</span> effusion.  Soft tissues are normal.</span> Here is some more <span class="phdefinition" keyword="party">party</span> text now.</div></body></html>`,
			[]string{"grapefruit", "christmas party", "party"},
		},
		{
			"term span term - regardless of letter case",
			[]byte(
				`<html><head></head><body><div class="t">Right GrapeFruit<span class="ff3">: No christmas party effusion.  Soft tissues are normal.</span> Here is some more party text now.</div></body></html>`,
			),
			`<html><head></head><body><div class="t">Right <span class="phdefinition organvizTerm" keyword="GrapeFruit">GrapeFruit</span><span class="ff3">: No <span class="phdefinition" keyword="christmas party">christmas party</span> effusion.  Soft tissues are normal.</span> Here is some more <span class="phdefinition" keyword="party">party</span> text now.</div></body></html>`,
			[]string{"GrapeFruit", "christmas party", "party"},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			lg := logrus.WithField("name", c.name)
			out, found, err := tg.TagFns[dicomutils.RegSR](context.Background(), lg, c.inputHTML)
			if err != nil {
				t.Fatalf("failed to tag HTML: %v", err)
			}
			if string(out) != c.expectedHTML {
				t.Fatalf("unexpected html output: \n%s\nexpected:\n%s", out, c.expectedHTML)
			}
			if !reflect.DeepEqual(c.expectedTermsFound, found) {
				t.Fatalf("unexpected terms found %v\nexpected: %v", found, c.expectedTermsFound)
			}
		})
	}
}
