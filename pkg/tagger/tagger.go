package tagger

import (
	"bytes"
	"context"
	"regexp"
	"slices"
	"strings"
	"unicode"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/reportprocessor/pkg/dicomutils"
	"golang.org/x/net/html"
)

var spaceRe = regexp.MustCompile(" |\u205F")
var whitespaceRe = regexp.MustCompile(" ")

type Tagger struct {
	root          *node
	termList      []string
	TagFns        map[dicomutils.ReportFormat]TagHTML
	organVizTerms []string
}

type TagHTML func(context.Context, *logrus.Entry, []byte) ([]byte, []string, error)

type node struct {
	phraseId int
	children map[string]*node
	termLen  int
}

func (d *Tagger) Build(termList []string, organVizTerms []string) {
	d.termList = termList
	d.organVizTerms = organVizTerms
	d.root = &node{
		children: make(map[string]*node),
	}
	for i, term := range termList {
		addPhrase(d.root, term, i+1)
	}

	d.TagFns = make(map[dicomutils.ReportFormat]TagHTML)
	d.TagFns[dicomutils.EncapPDF] = d.TagConvertedHTML
	d.TagFns[dicomutils.RegSR] = d.TagHTML
	d.TagFns[dicomutils.CareStreamSR] = d.TagHTML
}

func addPhrase(root *node, phrase string, phraseId int) {
	curnode := root
	// break phrase into words
	words := strings.Split(phrase, " ")
	totalLen := 0
	// start traversal at root
	for i := 0; i < len(words); i++ {
		// if the current word does not exist as a child
		// to current node, add it
		word := strings.ToLower(words[i])
		totalLen += len(word)
		if _, ok := curnode.children[word]; !ok {
			curnode.children[word] = &node{
				children: make(map[string]*node),
			}
		}
		// move traversal pointer to current word
		curnode = curnode.children[word]

		// if current word is the last one, mark it with
		// phrase Id
		if i == (len(words) - 1) {
			curnode.phraseId = phraseId
			curnode.termLen = len(phrase)
		}
	}
}

type phraseMap struct {
	phraseId int
	idx      int
	termLen  int
}

func (d *Tagger) findTerms(lg *logrus.Entry, textBody string) []phraseMap {
	// a pointer to traverse the trie without damaging
	// the original reference
	node := d.root

	//map of found ids and idx
	foundPhrases := []phraseMap{}

	// break text body into words, all lowercase
	textBody = strings.ToLower(textBody)

	//separate words on whitespace and the special
	//`&#8287;` html space code (`\u205F` in unicode)
	words := spaceRe.Split(textBody, -1)

	// starting traversal at trie root and first
	// word in text body
	idx := 0
	for i := 0; i < len(words); {
		word := trimPunctuation(lg, words[i])
		// if current node has current word as a child
		// move both node and words pointer forward
		if res, ok := node.children[word]; ok {
			// move trie pointer forward
			node = res

			// move words pointer forward
			i++
		} else {
			// current node does not have current
			// word in its children

			// if there is a phrase Id, then the previous
			// sequence of words matched a phrase, add Id to
			// found list

			//NOTE: instead of needing termList in this fn, could construct a function to travel back up
			//the trie to build the phrase, but accessing from the list seemed more straightforward.
			if node.phraseId != 0 {
				foundIdx := strings.Index(textBody[idx:], d.termList[node.phraseId-1])

				//if the foundIdx is -1, it means that the spaces in textBody are actually the character \u205F,
				//which is why strings.Index returns -1 since our termList contains normal whitespaces
				//what we will do in this case is make a copy of the found phrase and replace the whitespaces
				//in the copy with \u205F and do the regular operations, except use the length of the copied phrase
				//instead of node.termLen because the size of \u205F is 3 bytes, not 1
				if foundIdx == -1 {
					phrase := whitespaceRe.ReplaceAllString(d.termList[node.phraseId-1], "\u205F")
					foundIdx = idx + strings.Index(textBody[idx:], phrase)
					idx = foundIdx + len(phrase)
					foundPhrases = append(foundPhrases, phraseMap{node.phraseId, foundIdx, len(phrase)})

				} else {
					foundIdx = idx + foundIdx
					idx = foundIdx + node.termLen
					foundPhrases = append(foundPhrases, phraseMap{node.phraseId, foundIdx, node.termLen})
				}
			}

			if node == d.root {
				// if trie pointer is already at root, increment
				// words pointer
				i++
			} else {
				// if not, leave words pointer at current word
				// and return trie pointer to root
				node = d.root
			}

		}
	}

	// one case remains, word pointer as reached the end
	// and the loop is over but the trie pointer is pointing to
	// a phrase Id
	if node.phraseId != 0 {
		foundIdx := strings.Index(textBody[idx:], d.termList[node.phraseId-1])

		if foundIdx == -1 {
			phrase := whitespaceRe.ReplaceAllString(d.termList[node.phraseId-1], "\u205F")
			foundIdx = idx + strings.Index(textBody[idx:], phrase)
			foundPhrases = append(foundPhrases, phraseMap{node.phraseId, foundIdx, len(phrase)})

		} else {
			foundIdx = idx + foundIdx
			foundPhrases = append(foundPhrases, phraseMap{node.phraseId, foundIdx, node.termLen})
		}
	}
	return foundPhrases
}

func trimPunctuation(lg *logrus.Entry, str string) string {
	// Count start punctuation.
	removeFromStart := 0
	runes := []rune(str)
	for i := 0; i < len(runes); i++ {
		if unicode.IsPunct(runes[i]) {
			removeFromStart++
		} else {
			break
		}
	}

	// Count end punctuation.
	removeFromEnd := 0
	for i := len(runes) - 1; i >= 0; i-- {
		if unicode.IsPunct(runes[i]) {
			removeFromEnd++
		} else {
			break
		}
	}
	// No characters were punctuation.
	if removeFromStart == 0 && removeFromEnd == 0 {
		return str
	}
	// All characters were punctuation.
	if removeFromStart == len(str) && removeFromEnd == len(str) {
		return ""
	}
	// Substring.
	return str[removeFromStart : len(str)-removeFromEnd]
}

func (d *Tagger) TagHTML(
	ctx context.Context,
	lg *logrus.Entry,
	htmlBytes []byte,
) ([]byte, []string, error) {
	htmlStr := string(htmlBytes)

	doc, err := html.Parse(strings.NewReader(htmlStr))
	if err != nil {
		lg.WithError(err).Error("could not parse HTML")
		return nil, nil, err
	}
	totalFoundTerms := make([]string, 0)
	foundPhrases := make([]phraseMap, 0)
	var f func(*html.Node, string)
	f = func(n *html.Node, parentTag string) {
		if parentTag == "style" || parentTag == "script" {
			return //skip css and js
		}
		if n.Type == html.TextNode {
			foundPhrases = d.findTerms(lg, n.Data)
			if len(foundPhrases) > 0 {
				textStr := n.Data
				//if there are found phrases, need to build out a new Node for each untagged piece of text, the tagging span, and the wrapped text
				//ie, original string "This is a string, and here is my definition."
				//in output html should be "This is a string, and here is my <span class="phdefinition">definition</span>.
				//which means the current node has to be replaced with 3 nodes:
				//1. Textnode with data "This is a string, and here is my "
				//2. Element node with data "span", attribute key="class", value="definition"
				//3. Text node with data "definition" as child of span node
				parent := n.Parent
				nextSib := n.NextSibling
				startIdx := 0
				endIdx := startIdx
				for _, phrase := range foundPhrases {
					text := textStr[startIdx:phrase.idx]
					if text != "" {
						//there is text in this node BEFORE a found phrase. Create
						//a new node for this section
						insertNode(text, html.TextNode, nil, nextSib, parent)
					}
					endIdx = phrase.idx + phrase.termLen
					term := textStr[phrase.idx:endIdx]

					customClasses := "phdefinition"
					if slices.Contains(d.organVizTerms, strings.ToLower(term)) {
						customClasses = customClasses + " organvizTerm"
					}
					//create span node to tag the found phrase (note: this is just the <span> piece. The phrase goes in the next one)
					newSpanNode := insertNode("span", html.ElementNode, []html.Attribute{
						{
							Key: "class",
							Val: customClasses,
						},
						{
							Key: "keyword",
							Val: term,
						},
					}, nextSib, parent)
					//add the phrase to be tagged as a child of the span (no siblings)
					insertNode(term, html.TextNode, nil, nil, newSpanNode)
					totalFoundTerms = append(totalFoundTerms, term)
					startIdx = endIdx
				}
				//add any remaining text after the phrase as another text node
				insertNode(textStr[endIdx:], html.TextNode, nil, nextSib, parent)

				//remove original node (we've replaced it now)
				parent.RemoveChild(n)
				if nextSib != nil {
					//if there is a sibling to this node, call the function again.
					//ie, with original html <div>Some text then<span> a span</span> then some more text</div>
					//the node that's just been completed is "Some text then"
					//the below function call will call f() on <span> (which will, in turn, evaluate its child "a span" recursively)
					//have to do this here because in the recursive loop below, when we remove n, it won't traverse the next sibling
					for c := nextSib; c != nil; c = c.NextSibling {
						f(c, parent.Data)
					}
				}
			}
		}
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			f(c, n.Data)
		}

	}
	f(doc, "")

	var buf = new(bytes.Buffer)
	err = html.Render(buf, doc)
	if err != nil {
		lg.WithError(err).Error("error rendering tagged html")
		return nil, nil, err
	}
	return buf.Bytes(), totalFoundTerms, nil
}

func insertNode(
	text string,
	t html.NodeType,
	attr []html.Attribute,
	nextSib *html.Node,
	parent *html.Node,
) *html.Node {
	newNode := &html.Node{
		Data: text,
		Type: t,
		Attr: attr,
	}
	parent.InsertBefore(newNode, nextSib)
	return newNode
}

// tag HTML that was converted using the pdf2htmlEX tool
// since the report converted by pdf2htmlex is always structured in this format, we don't need to use recursion and
// can directly use loops to get into the divs containing text
func (d *Tagger) TagConvertedHTML(
	ctx context.Context,
	lg *logrus.Entry,
	htmlBytes []byte,
) ([]byte, []string, error) {
	htmlStr := string(htmlBytes)
	totalFoundTerms := make([]string, 0)

	doc, err := html.Parse(strings.NewReader(htmlStr))
	if err != nil {
		lg.WithError(err).Error("could not parse HTML")
		return nil, nil, err
	}

	//this is where the id="page-container" is located, which is exactly where all the text needed to be tagged should be located
	//if the pdf was converted using pdf2htmlEX then the id="page-container" div is guarenteed to be here, and all text content
	//of the html is located inside this main div
	pageContainer := doc.FirstChild.NextSibling.NextSibling.FirstChild.NextSibling.NextSibling.FirstChild.NextSibling.NextSibling.NextSibling

	//loop over all of the pageContainer's individual pages
	for page := pageContainer.FirstChild; page != nil; page = page.NextSibling {

		//the page-container's children weave blank textnodes between
		//each page for some reason, we can ignore those
		if page.Type != html.TextNode {
			subPage := page.FirstChild

			//iterate over the sections or text containers of the subpage
			for section := subPage.FirstChild; section != nil; section = section.NextSibling {

				//sometimes the tool does not separate subPage into sections (i.e. header, body, footer) so we will need to determine
				//what we are dealing with. if class="t", it is a text container, if class="c", it is separated into sections
				for _, attribute := range section.Attr {
					if attribute.Key == "class" {

						class := strings.Split(attribute.Val, " ")
						for _, value := range class {
							if value == "t" {
								classes := getStyleClasses(section)
								totalFoundTerms = append(
									totalFoundTerms,
									d.tagTerms(lg, section, classes, false)...)
								break
							} else if value == "c" {
								//loop over the children of the sections containing the text content
								for textContainer := section.FirstChild; textContainer != nil; textContainer = textContainer.NextSibling {
									classes := getStyleClasses(textContainer)
									totalFoundTerms = append(totalFoundTerms, d.tagTerms(lg, textContainer, classes, false)...)
								}
								break
							}
						}
						break
					}
				}
			}
		}
	}

	var buf = new(bytes.Buffer)
	err = html.Render(buf, doc)
	if err != nil {
		lg.WithError(err).Error("error rendering tagged html")
		return nil, nil, err
	}
	return buf.Bytes(), totalFoundTerms, nil
}

// return a map of strings that consist of important
// font styling classes used in the pdf2htmlEX tool
// we will get anything in the form of: ff[0-9] (font family),
// fc[0-9] (font colour), and fs[0-9] (font size)
// the map will contain them in key: "ff", value: "[0-9]" pairs
func getStyleClasses(n *html.Node) map[string]string {
	classes := make(map[string]string)

	for _, attribute := range n.Attr {
		if attribute.Key == "class" {
			for _, style := range strings.Split(attribute.Val, " ") {
				if strings.Contains(style, "ff") || strings.Contains(style, "fc") ||
					strings.Contains(style, "fs") {
					classes[style[0:2]] = style[2:]
				}
			}
			break
		}
	}
	return classes
}

// tag all keywords found in the textNode n
// styleClasses contains a map of parent classes from either the "t" div or a nested span, which we need to
//
//	apply on keywords and nested text to preserve the styling on the front end
//
// nested is a flag that tells us if we're currently working on a nested span, where
//
//	we will need to add the styleClasses if they are not already set
func (d *Tagger) tagTerms(
	lg *logrus.Entry,
	n *html.Node,
	styleClasses map[string]string,
	nested bool,
) []string {
	var text string
	foundTerms := make([]string, 0)

	//if we are in a nested span, lets add the parent's text styling
	//if it is not overriden by the span
	//e.x.
	//styleClasses = ["ff0", "fs2"]
	//n = <span class="ff6">nested text</span> -> n = <span class="ff6 fs2">nested text</span>
	if nested {
		for index := range n.Attr {
			if n.Attr[index].Key == "class" {
				for style, value := range styleClasses {
					if !strings.Contains(n.Attr[index].Val, style) {
						n.Attr[index].Val = n.Attr[index].Val + " " + style + value

						//if the class substring is already present in the nested span,
						//the children of this nested span must inherit the span's class
						//not the parent "t" div
					} else {

						classes := strings.Split(n.Attr[index].Val, " ")
						for _, class := range classes {
							if strings.Contains(class, style) {
								//if style is contained in class then len(class) >= 2 must hold
								styleClasses[style] = class[2:]
								break
							}
						}
					}
				}
				break
			}
		}
	}

	var styleString string
	for style, value := range styleClasses {
		styleString = styleString + " " + style + value
	}

	//build the entire string contained in this div
	for textContent := n.FirstChild; textContent != nil; textContent = textContent.NextSibling {
		//if the type is textnode, add the string to text
		if textContent.Type == html.TextNode {
			text = text + textContent.Data

		} else {
			if textContent.Type == html.ElementNode {
				if textContent.FirstChild != nil {
					//special case where text could be further nested inside a <span> tag
					//for example,
					//  th
					//	<span></span>
					//  is a
					//  <span>
					//    sente
					//    <span></span>
					//    nce
					//  </span>
					//in this case we can recusively call this function on the span containing the text
					//NOTE: this type of nesting generally occurs at the very end of the entire string
					//and is separated like this mostly for formatting/styling purposes, so chances that
					//it is part of a bigger phrase is unlikely
					foundTerms = append(foundTerms, d.tagTerms(lg, textContent, styleClasses, true)...)
				}
			}
		}
	}

	//get a list of found phrases from the built string
	//should be sorted in order of phrase idx ascending
	foundPhrases := d.findTerms(lg, text)

	//we've found some matching phrases, we now have to tag
	//the appropriate parts in the html file
	if len(foundPhrases) > 0 {

		//comparisonString is a variable that we will slowly build up text in,
		//very similar to the text variable used above
		//as we iterate over the text in the class="t" div, we will constantly add
		//more text to comparisonString and use it to determine when we should start appending
		//textNodes or spans into the phdefinition span, based on the starting index of phrase
		var previousString, comparisonString string
		textNode := n.FirstChild
		for _, phrase := range foundPhrases {
			keyword := spaceRe.ReplaceAllString(text[phrase.idx:phrase.idx+phrase.termLen], " ")
			foundTerms = append(foundTerms, keyword)
			//a flag to tell us whether or not we need, or have already fixed a prefix issue
			prefix := true
			
			customClasses := "phdefinition"
			if slices.Contains(d.organVizTerms, strings.ToLower(keyword)) {
				customClasses = customClasses + " organvizTerm"
			}
			//create a new span html tag to contain the term and styling
			span := &html.Node{
				Data: "span",
				Type: html.ElementNode,
				Attr: []html.Attribute{
					{
						Key: "class",
						Val: customClasses + styleString,
					},
					{
						Key: "keyword",
						Val: keyword,
					},
				},
			}

			//in all the comments below, we will refer to the phrase that needs to be tagged as 'KEYWORD'
			for textNode != nil {

				//if the node type is a textNode, build up the string
				if textNode.Type == html.TextNode {
					previousString = comparisonString
					comparisonString = comparisonString + textNode.Data
				}

				//if the string we are building is now longer than the starting index
				//of the found phrase, we know that the textnode we just added contains
				//the starting point of the phrase
				if len(comparisonString) >= phrase.idx {

					//if the length of the comparisonString is greater than the
					//phrase's start index plus the phrase's length, then the textNode
					//we are on contains the entire phrase, a suffix that we must truncate,
					//and possibly contains a prefix we must also remove
					//e.x. textNode.Data = "KEYWORD a suffix", "a prefix KEYWORD a suffix", " KEYWORD "
					//in this case we will:
					//1. create a new textnode containing the prefix of the keyword, and insert it before the current textNode
					//2. create a new textNode containing only the keyword, and again insert it before textNode
					//3. modify textNode.Data to truncate the prefix and keyword, leaving only the suffix
					//4. modify comparisonString to contain everything up to the keyword
					//5. move onto the next phrase with textNode
					if prefix && len(comparisonString) > phrase.idx+phrase.termLen {

						prefixNode := &html.Node{
							Data: comparisonString[len(previousString):phrase.idx],
							Type: html.TextNode,
						}
						n.InsertBefore(prefixNode, textNode)

						keyword := &html.Node{
							Data: comparisonString[phrase.idx : phrase.idx+phrase.termLen],
							Type: html.TextNode,
						}
						span.AppendChild(keyword)
						n.InsertBefore(span, textNode)

						textNode.Data = comparisonString[phrase.idx+phrase.termLen:]
						comparisonString = comparisonString[:phrase.idx+phrase.termLen]
						prefix = false
						break
					}

					//if the comparisonString does not contain a substring of textNode.Data,
					//we know that textNode.Data contains a prefix that must be truncated
					//it is guarenteed that the keyword will not have a suffix, as it will be
					//caught by the case above
					//e.x. textNode.Data = "a prefix KEYWORD", "a prefix KEY", "prefix K"
					//in this case we will:
					//1. create a new textnode containing the prefix of the keyword, and insert it before the current textNode
					//2. modify textNode.Data and truncate the prefix
					//3. modify comparisonString to only contain everything before the starting index of the phrase
					//4. continue in the current loop as we have not tagged the full keyword yet
					if prefix && !strings.Contains(comparisonString[phrase.idx:], textNode.Data) {
						prefixText := &html.Node{
							Data: comparisonString[len(previousString):phrase.idx],
							Type: html.TextNode,
						}

						n.InsertBefore(prefixText, textNode)
						textNode.Data = comparisonString[phrase.idx:]
						comparisonString = comparisonString[:phrase.idx]

						prefix = false
						continue
					}
					prefix = false

					//if the comparionsString's length is equal to exactly the length of the phrase plus its index,
					//we know that this textNode is the final node containing text related to the phrase
					//e.x. textNode.Data = "KEYWORD", "EYWORD", "WORD", "D"
					//in this case we will:
					//1. copy the textNode and remove it from n, then append it to our custom phdefinition span
					//2. move the textNode forwards to the next sibling since we are finished with this phrase
					//3. insert the custom span before the textNode, and break out of the loop, moving onto the next phrase
					if len(comparisonString) == phrase.idx+phrase.termLen {
						temp := textNode
						textNode = textNode.NextSibling
						n.RemoveChild(temp)
						span.AppendChild(temp)
						n.InsertBefore(span, textNode)
						break

						//if the comparisonString's length is greater than the phrase's starting index plus the length,
						//we know that this textNode is the final node containing text related to the phrase, and it
						//contains a suffix that we must remove
						//e.x. textNode.Data = "KEYWORD a suffix", "EYWORD a suffix", "D a suffix"
						//in this case we will:
						//1. create a new textNode containing the rest of the phrase and append it to the custom span
						//2. modify textNode.Data to truncate any part of the phrase
						//3. modify comparisonString to contain everything up to the phrase including the phrase
						//4. insert the custom span before the textNode
						//5. break out of the loop and move onto the next phrase
					} else if len(comparisonString) > phrase.idx+phrase.termLen {
						removeSuffix := &html.Node{
							Data: comparisonString[len(previousString) : phrase.idx+phrase.termLen],
							Type: html.TextNode,
						}
						span.AppendChild(removeSuffix)
						textNode.Data = comparisonString[phrase.idx+phrase.termLen:]
						comparisonString = comparisonString[:phrase.idx+phrase.termLen]
						n.InsertBefore(span, textNode)
						break

						//in this final case, as nothing above has been triggered, we know that the current textNode contains
						//the middle section of the phrase, and can be either a textNode or a span element, which we will append
						//to our custom span
						//e.x. textNode.Data = <span class="_ _[a-z0-9]+"></span>, "EYWOR", "E", "Y", "YWO", "WO"
						//in this case we will:
						//1. copy the textNode and remove it from n
						//2. append the copy to the custom span
						//3. move the textNode forward and continue through the loop as we have not finished tagging the phrase yet
					} else {
						temp := textNode
						textNode = textNode.NextSibling
						n.RemoveChild(temp)
						span.AppendChild(temp)
						continue
					}
				}
				//if we ever reach here, it means that we've not come across any nodes that includes a phrase yet
				textNode = textNode.NextSibling
			}
		}
	}
	return foundTerms
}
