package dicomutils

import (
	"github.com/su<PERSON><PERSON><PERSON>/dicom"
	"github.com/suyas<PERSON><PERSON>/dicom/pkg/tag"
)

type ReportFormat string

const (
	None         ReportFormat = "none"
	EncapPDF     ReportFormat = "encapsulatedPDF"
	RegSR        ReportFormat = "regularSR"
	CareStreamSR ReportFormat = "carestreamSR"
	KonicaSR     ReportFormat = "konicaSR"
	Other        ReportFormat = "other"
)

func isEncapsulatedPDF(ds dicom.Dataset) bool {
	if sopClassUID, err := ds.FindElementByTag(tag.SOPClassUID); err == nil {
		if sopClassUID.Value.String() == "[1.2.840.10008.5.1.4.1.1.104.1]" {
			return true
		}
	}

	return false
}

func GetReportFormatFromFile(path string) (ReportFormat, error) {
	dataSet, err := dicom.ParseFile(path, nil)
	if err != nil {
		return None, err
	}

	return GetReportFormat(dataSet), nil
}

func GetReportFormat(ds dicom.Dataset) ReportFormat {
	if isEncapsulatedPDF(ds) {
		return EncapPDF
	}
	return getSRPACSType(ds)
}

// this logic is very specific to the providers PH integrates with. Some of the facilities that use DicomSR
// format things in a very non-standard way, so its important to know which one we're dealing with
func getSRPACSType(ds dicom.Dataset) ReportFormat {
	modality, err := ds.FindElementByTag(tag.Modality)
	if err != nil {
		return None
	}

	var modVal string
	v, ok := modality.Value.GetValue().([]string)
	if ok {
		modVal = v[0]
	} else {
		return None
	}

	var descVal string
	seriesDescription, err := ds.FindElementByTag(tag.SeriesDescription)
	if err == nil {
		v, ok = seriesDescription.Value.GetValue().([]string)
		if ok {
			descVal = v[0]
		}
	}
	// check if report is a CareStream report
	if (modVal == "SR") && descVal == "Carestream PACS Reports" {
		return CareStreamSR
	}

	// check if report is a Adams diagnostic imaging (konica report)
	if (modVal == "SR") && descVal == "ADAMS DIAGNOSTIC IMAGING" {
		return KonicaSR
	}

	// check if report is any other SR report
	if modVal == "SR" {
		return RegSR
	}

	return None
}
