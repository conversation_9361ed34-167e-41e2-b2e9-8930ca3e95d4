package auth

import (
	"context"
	"testing"
)

// define authStore
type TestAuthStore struct{}

func (tas *TestAuthStore) GetAPIUser(name string) (APIUser, error) {
	return APIUser{
		KeyHash: "fd222b13f7c67e748a547289173c839b15662171515cac01b3257a7dee4bffa7",
		Role:    Read | Write,
	}, nil
}

func TestAuthUser(t *testing.T) {

	t.Run("blank key hash", func(t *testing.T) {
		a := Authenticator{
			AuthStore: &TestAuthStore{},
			APIUsers:  make(map[string]APIUser, 0),
		}
		_, gotErr := a.authUser(context.Background(), "")
		if gotErr == nil {
			t.Fatal("Blank api key should give error")
		}
	})
	t.Run("bad key format - not basic", func(t *testing.T) {
		a := Authenticator{
			AuthStore: &TestAuthStore{},
			APIUsers:  make(map[string]APIUser, 0),
		}
		_, gotErr := a.authUser(context.Background(), "123345")
		if gotErr == nil {
			t.Fatal("auth not in Basic format should give error")
		}
	})
	t.Run("bad key format - not b64encoded", func(t *testing.T) {
		a := Authenticator{
			AuthStore: &TestAuthStore{},
			APIUsers:  make(map[string]APIUser, 0),
		}
		_, gotErr := a.authUser(context.Background(), "Basic 12345")
		if gotErr == nil {
			t.Fatal("creds not in b64 should give error")
		}
	})
	t.Run("bad key format - bad credential format", func(t *testing.T) {
		a := Authenticator{
			AuthStore: &TestAuthStore{},
			APIUsers:  make(map[string]APIUser, 0),
		}
		//this is b64 encoded, but is not name:key
		_, gotErr := a.authUser(
			context.Background(),
			"Basic TUpSWVNkYWJrWnAyMnpITDZnQ25vU2Y0UjE4am1RMHRlRWRqNTdSYm5rVXl6a3htZ1hXeHFWMnJYTE9PaWtQag==",
		)
		if gotErr == nil {
			t.Fatal("creds not in name:key format should give error")
		}
	})
	t.Run("good key format", func(t *testing.T) {
		a := Authenticator{
			AuthStore: &TestAuthStore{},
			APIUsers:  make(map[string]APIUser, 0),
		}
		//"core:MJRYSdabkZp22zHL6gCnoSf4R18jmQ0teEdj57RbnkUyzkxmgXWxqV2rXLOOikPj"
		//hash: fd222b13f7c67e748a547289173c839b15662171515cac01b3257a7dee4bffa7
		role, gotErr := a.authUser(
			context.Background(),
			"Basic Y29yZTpNSlJZU2RhYmtacDIyekhMNmdDbm9TZjRSMThqbVEwdGVFZGo1N1JibmtVeXpreG1nWFd4cVYyclhMT09pa1Bq",
		)
		if gotErr != nil {
			t.Fatal("valid cred format should not have thrown error")
		}
		//hash should match, so role should be RW
		if role&Read == 0 || role&Write == 0 {
			t.Fatalf("expected rw perms, got %v", role)
		}
	})

	t.Run("good key format, bad creds", func(t *testing.T) {
		a := Authenticator{
			AuthStore: &TestAuthStore{},
			APIUsers:  make(map[string]APIUser, 0),
		}
		//"core:12345"
		_, gotErr := a.authUser(context.Background(), "Basic Y29yZToxMjM0NQ==")
		if gotErr == nil {
			t.Fatal("invalid creds should throw error")
		}
	})

}

func TestAuthCache(t *testing.T) {

	// happy cases
	t.Run("get rw user role", func(t *testing.T) {
		a := Authenticator{
			AuthStore: &TestAuthStore{},
			APIUsers: map[string]APIUser{
				"core": {
					"fd222b13f7c67e748a547289173c839b15662171515cac01b3257a7dee4bffa7",
					Read | Write,
				},
			},
		}

		role, err := a.authUser(
			context.Background(),
			"Basic Y29yZTpNSlJZU2RhYmtacDIyekhMNmdDbm9TZjRSMThqbVEwdGVFZGo1N1JibmtVeXpreG1nWFd4cVYyclhMT09pa1Bq",
		)
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}

		if role&Read == 0 {
			t.Fatalf("expected role to have read permissions, got %d", role)
		}

		if role&Write == 0 {
			t.Fatalf("expected role to have write permissions, got %d", role)
		}
	})

	t.Run("get readonly user role", func(t *testing.T) {
		a := Authenticator{
			AuthStore: &TestAuthStore{},
			APIUsers: map[string]APIUser{
				"core": {"fd222b13f7c67e748a547289173c839b15662171515cac01b3257a7dee4bffa7", Read},
			},
		}
		role, err := a.authUser(
			context.Background(),
			"Basic Y29yZTpNSlJZU2RhYmtacDIyekhMNmdDbm9TZjRSMThqbVEwdGVFZGo1N1JibmtVeXpreG1nWFd4cVYyclhMT09pa1Bq",
		)
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}

		if role&Read == 0 {
			t.Fatalf("expected role to have read permissions, got %d", role)
		}

		if role&Write != 0 {
			t.Fatalf("expected role to not have write permissions, got %d", role)
		}
	})

	t.Run("get writeonly user role", func(t *testing.T) {
		a := Authenticator{
			AuthStore: &TestAuthStore{},
			APIUsers: map[string]APIUser{
				"core": {"fd222b13f7c67e748a547289173c839b15662171515cac01b3257a7dee4bffa7", Write},
			},
		}
		role, err := a.authUser(
			context.Background(),
			"Basic Y29yZTpNSlJZU2RhYmtacDIyekhMNmdDbm9TZjRSMThqbVEwdGVFZGo1N1JibmtVeXpreG1nWFd4cVYyclhMT09pa1Bq",
		)
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}

		if role&Read != 0 {
			t.Fatalf("expected role to not have read permissions, got %d", role)
		}

		if role&Write == 0 {
			t.Fatalf("expected role to have write permissions, got %d", role)
		}
	})

	t.Run("get zero permission user role", func(t *testing.T) {
		a := Authenticator{
			AuthStore: &TestAuthStore{},
			APIUsers: map[string]APIUser{
				"core": {"fd222b13f7c67e748a547289173c839b15662171515cac01b3257a7dee4bffa7", 0},
			},
		}
		role, err := a.authUser(
			context.Background(),
			"Basic Y29yZTpNSlJZU2RhYmtacDIyekhMNmdDbm9TZjRSMThqbVEwdGVFZGo1N1JibmtVeXpreG1nWFd4cVYyclhMT09pa1Bq",
		)
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}

		if role&Read != 0 {
			t.Fatalf("expected role to not have read permissions, got %d", role)
		}

		if role&Write != 0 {
			t.Fatalf("expected role to not have write permissions, got %d", role)
		}
	})

	t.Run("cache user", func(t *testing.T) {
		a := Authenticator{
			AuthStore: &TestAuthStore{},
			APIUsers:  make(map[string]APIUser, 0),
		}
		role, err := a.authUser(
			context.Background(),
			"Basic Y29yZTpNSlJZU2RhYmtacDIyekhMNmdDbm9TZjRSMThqbVEwdGVFZGo1N1JibmtVeXpreG1nWFd4cVYyclhMT09pa1Bq",
		)
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}

		if role&Read == 0 {
			t.Fatalf("expected role to have read permissions, got %d", role)
		}

		if role&Write == 0 {
			t.Fatalf("expected role to have write permissions, got %d", role)
		}

		//expect that the APIUsers map now has one entry with Read|Write perms
		if len(a.APIUsers) != 1 {
			t.Fatalf("expected user cache to have 1 entry, has %d", len(a.APIUsers))
		}

		for key, _ := range a.APIUsers {
			if a.APIUsers[key].Role&Read == 0 || a.APIUsers[key].Role&Write == 0 {
				t.Fatalf("Expected cached user to have read-write perms, had %v", a.APIUsers[key])
			}
		}
	})

}
