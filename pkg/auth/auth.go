package auth

import (
	"context"
	"crypto/sha256"
	b64 "encoding/base64"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type APIAuthRole uint

// ie, a RW user would have APIAuthRole = 3
const (
	Read APIAuthRole = 1 << iota
	Write
)

type APIUser struct {
	KeyHash string
	Role    APIAuthRole
}

type Authenticator struct {
	AuthStore AuthStore
	APIUsers  map[string]APIUser //map of user names to key and role info
}

const RoleContextKey = "role"

func (a *Authenticator) validateAuth(w http.ResponseWriter, r *http.Request) (APIAuthRole, bool) {
	token := r.Header.Get("Authorization")
	role, err := a.authUser(r.Context(), token)
	if err != nil {
		http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
		return role, false
	}
	return role, true
}

// auth middleware - called on authenticated routes before the handler is called
func (a *Authenticator) ValidateAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		role, isAuthed := a.validateAuth(w, r)
		if !isAuthed {
			return
		}

		//set the role in the request context
		ctx := context.WithValue(r.Context(), RoleContextKey, role)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (a *Authenticator) ValidateReadRole(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		role, isAuthed := a.validateAuth(w, r)
		if !isAuthed {
			return
		}
		//set the role in the request context
		ctx := context.WithValue(r.Context(), RoleContextKey, role)

		if role&Read == 0 {
			logutils.CtxLogger(ctx).WithField(RoleContextKey, role).Error("insufficient permissions")
			http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
			return
		}

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (a *Authenticator) authUser(ctx context.Context, authHeader string) (APIAuthRole, error) {
	if authHeader == "" {
		err := errors.New("no authHeader")
		logutils.CtxLogger(ctx).WithError(err).Error("no authHeader")
		return 0, err
	}

	authParts := strings.Split(string(authHeader), " ")
	if len(authParts) != 2 || authParts[0] != "Basic" {
		err := errors.New("auth header has incorrect format")
		logutils.CtxLogger(ctx).WithError(err).Error("invalid auth header")
		return 0, err
	}

	//expect format `Basic <name:key>` , where name:key is b64 encoded
	credBytes, err := b64.StdEncoding.DecodeString(authParts[1])
	if err != nil {
		logutils.CtxLogger(ctx).WithError(err).Error("invalid auth header")
		return 0, err
	}

	credString := string(credBytes)
	creds := strings.Split(credString, ":")
	if len(creds) != 2 {
		err := errors.New("auth creds have incorrect format")
		logutils.CtxLogger(ctx).WithError(err).Error("invalid auth header")
		return 0, err
	}

	givenKeyHash := getKeyHash(ctx, creds[1])
	if givenKeyHash == "" {
		err := errors.New("invalid key")
		logutils.CtxLogger(ctx).WithError(err).Error("invalid key hash")
		return 0, err
	}

	if user, ok := a.APIUsers[creds[0]]; ok && user.KeyHash == givenKeyHash {
		return user.Role, nil
	} else {
		user, err := a.AuthStore.GetAPIUser(creds[0])
		if err != nil {
			logutils.CtxLogger(ctx).WithError(err).Error("no match for user")
			return 0, err
		}
		if user.KeyHash == givenKeyHash {
			a.APIUsers[creds[0]] = user
			return user.Role, nil
		}
		logutils.CtxLogger(ctx).Error("keyhash does not match")
		return 0, fmt.Errorf("Unauthorized.")
	}
}

func getKeyHash(ctx context.Context, apiKey string) (keyHash string) {
	h := sha256.New()
	_, err := h.Write([]byte(apiKey))
	if err != nil {
		logutils.CtxLogger(ctx).WithError(err).Error("error writing sha256 hash for api key")
		return ""
	}
	return fmt.Sprintf("%x", h.Sum(nil))
}

func GetContextRole(ctx context.Context) APIAuthRole {
	role, ok := ctx.Value(RoleContextKey).(APIAuthRole)
	if !ok {
		return 0
	}
	return role
}
