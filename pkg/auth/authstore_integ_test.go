//go:build integration
// +build integration

package auth

import (
	"database/sql"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/reportprocessor/pkg/testutils"
)

func TestGetKeyHash(t *testing.T) {
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	logrus.SetLevel(logrus.DebugLevel)

	db, err := sql.Open("mysql", cfg.SqlConnString)
	if err != nil {
		t.Fatal("can't connect to mysql", err)
	}
	a := SQLAuthStore{DB: db}

	t.Run("user doesn't exist", func(t *testing.T) {
		got, err := a.GetAPIUser("thisisafakename")
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}

		//non existent user should have 0 permissions
		if got.Role&Read != 0 || got.Role&Write != 0 {
			t.Fatalf("expected to return 0 permissions, got %v", got)
		}
	})

	t.Run("user does exist, has permissions", func(t *testing.T) {
		got, err := a.GetAPIUser("core")
		if err != nil {
			t.Fatalf("got error when expected none: %v", err)
		}
		expectedPerms := Read | Write
		if got.Role != expectedPerms {
			t.Fatalf("expected %v permissions, got %v", expectedPerms, got)
		}
	})
}
