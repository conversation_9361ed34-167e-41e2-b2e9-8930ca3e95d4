package auth

import "database/sql"

type AuthStore interface {
	GetAPIUser(string) (APIUser, error)
}

type SQLAuthStore struct {
	DB *sql.DB
}

func NewSQLAuthStore(db *sql.DB) AuthStore {
	return &SQLAuthStore{DB: db}
}

func (as *SQLAuthStore) GetAPIUser(name string) (APIUser, error) {

	var sqlUser APIUser
	err := as.DB.QueryRow("SELECT key_hash, type FROM auth_keys where name = ?", name).
		Scan(&sqlUser.KeyHash, &sqlUser.Role)
	if err != nil {
		if err == sql.ErrNoRows {
			return APIUser{}, nil
		}
		return APIUser{}, err
	}
	return sqlUser, nil
}
