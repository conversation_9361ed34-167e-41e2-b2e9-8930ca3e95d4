package convert

import (
	"os"
	"os/exec"
	"regexp"
	"strings"

	"github.com/sirupsen/logrus"
	"github.com/suyashkumar/dicom"
	"github.com/suyashkumar/dicom/pkg/tag"
	"gitlab.com/pockethealth/reportprocessor/pkg/dicomutils"
)

var spaceRe = regexp.MustCompile("\uE003")

// getHtmlFromDicom reads the dicom file at path and returns the HTML
func HtmlFromDicomSR(lg *logrus.Entry, path string, reportFormat dicomutils.ReportFormat) ([]byte, error) {
	var htmlReport []byte
	var err error
	if reportFormat == dicomutils.CareStreamSR {
		// CareStream SRs put the HTML directly into one of the DICOM tags
		htmlReport, err = parseCarestreamSRToHTML(path)
	} else {
		// dsr2html is standard dcmtk way of converting SR to html.
		// the additional flags ignore errors and assume a default charset for cases where reports have special chars.
		htmlReport, err = cmdWithOutput(lg, "dsr2html", []string{"-Er", "-Ev", "-Ec", "-Ee", "-Ei", "-Dv", "+Ca", "latin-1", path})

		// Konica SRs may have extra HTML that renders ugly
		// so manually strip out that extra text
		if reportFormat == dicomutils.KonicaSR {
			reImpressions := regexp.MustCompile(`(<div>\s*[\r\n]*\s*<b>Html Impression:((.)|[\r\n])*?<\/div>)`)
			htmlReport = []byte(reImpressions.ReplaceAllString(string(htmlReport), ""))
			reFinding := regexp.MustCompile(`(<div>\s*[\r\n]*\s*<b>Html Finding:((.)|[\r\n])*?<\/div>)`)
			htmlReport = []byte(reFinding.ReplaceAllString(string(htmlReport), ""))
		}
	}
	return htmlReport, err
}

// convertDicomToHtml converts the DICOM report at path to HTML and saves it as {path.html}.
// Returns an error if the operation failed.
func convertDicomToHtml(lg *logrus.Entry, path string, reportFormat dicomutils.ReportFormat) ([]byte, error) {
	htmlReport, err := HtmlFromDicomSR(lg, path, reportFormat)
	if err != nil || len(string(htmlReport[:])) == 0 {
		lg.WithError(err).Error("Get report html failed ")
		return nil, err
	}
	return htmlReport[:], nil
}

// converDicomToPdf converts the DICOM report at path to PDF and saves it as {path}.pdf.
// Returns the command's combined standard output and standard error
func convertDicomToPdf(lg *logrus.Entry, path string) ([]byte, error) {
	return cmdCombinedOutput(lg, "dcm2pdf", []string{path, path + ".pdf"})
}

// convertPdfToPng converts the PDF file at {path}.pdf to PNG and saves it as {path}.png
// Returns the command's combined standard output and standard error
func convertPdfToPng(lg *logrus.Entry, path string) ([]byte, error) {
	return cmdCombinedOutput(
		lg,
		"convert",
		[]string{"-density", "150", "-append", path + ".pdf", path + ".png"},
	)
}

// convertHtmlToPdf converts the HTML file at {path}.html to PDF and saves it as {path}.pdf
// Returns the command's combined standard output and standard error
func convertHtmlToPdf(lg *logrus.Entry, path string) ([]byte, error) {
	return cmdCombinedOutput(
		lg,
		"xvfb-run",
		[]string{"-a", "wkhtmltopdf", path + ".html", path + ".pdf"},
	)
}

// convertHtmlToPng converts the HTML file at {path}.html to PNG and saves it as {path}.png
// Returns the command's combined standard output and standard error
func convertHtmlToPng(lg *logrus.Entry, path string) ([]byte, error) {
	return cmdCombinedOutput(
		lg,
		"xvfb-run",
		[]string{"-a", "wkhtmltoimage", path + ".html", path + ".png"},
	)
}

// convertDicomToPng converts the DICOM report at path to PNG and saves it as {path}.png
// If successful, returns the standard output and standard error of the dcmj2pnm command.
// If an error occurs, stops and returns the standard output and standard error of the last command executed.
func convertDicomToPng(lg *logrus.Entry, path string) ([]byte, error) {
	stdouterr, err := cmdCombinedOutput(lg, "gdcmconv", []string{"--raw", path, path + ".raw"})
	if err != nil {
		lg.WithField("stdouterr", string(stdouterr)).WithError(err).Error("Get report raw failed")
		return stdouterr, err
	}
	stdouterr, err = cmdCombinedOutput(
		lg,
		"dcmj2pnm",
		[]string{"+on", "-mf", "+Wm", path + ".raw", path + ".png"},
	)
	if err != nil {
		lg.WithField("stdouterr", string(stdouterr)).
			WithError(err).
			Error("Get report png from raw failed")
		return stdouterr, err
	}
	return stdouterr, err
}

// convert dicom report at {path} to pdf and return associated bytes or convert
// to html or png without returning associated bytes.
// html will be stored at {path}.html, png will be stored at {path}.png.
func ConvertReport(accept string, lg *logrus.Entry, path string) ([]byte, dicomutils.ReportFormat, error) {
	repFormat, err := dicomutils.GetReportFormatFromFile(path)
	if err != nil {
		lg.WithError(err).Error("Get report format failed")
	}
	lg = lg.WithField("is_encap", repFormat == dicomutils.EncapPDF)

	if repFormat == dicomutils.EncapPDF {
		stdouterr, err := convertDicomToPdf(lg, path)
		pdf, err := os.ReadFile(path + ".pdf") // #nosec G304
		if err != nil || len(pdf) == 0 {
			lg.WithError(err).Error("error reading report pdf file after convertDicomToPdf")
			return nil, repFormat, err
		}
		if err != nil {
			lg.WithField("stdouterr", string(stdouterr)).
				WithError(err).
				Error("Get report dcm2pdf failed")
		}
		if accept == "application/pdf" {
			pdfBytes, err := os.ReadFile(path + ".pdf") // #nosec G304
			if err != nil {
				lg.WithError(err).Error("error reading report pdf file after convertDicomToPdf")
				return nil, repFormat, err
			}
			return pdfBytes, repFormat, nil
		} else if accept == "text/html" {
			htmlBytes, err := convertPdfToHtml(lg, path)
			if err != nil {
				lg.WithError(err).Error("Get report convert htmlfailed")
			}
			return htmlBytes, repFormat, err
		} else {
			stdouterr, err := convertPdfToPng(lg, path)
			if err != nil {
				lg.WithField("stdouterr", string(stdouterr)).WithError(err).Error("Get report convert failed")
			}
			pngBytes, err := os.ReadFile(path + ".png") // #nosec G304
			if err != nil {
				lg.WithError(err).Error("error reading report png file after convertPdfToPng")
				return nil, repFormat, err
			}
			return pngBytes, repFormat, err
		}
	} else if repFormat != dicomutils.None {
		// parse report format
		lg = lg.WithField("report_format", repFormat)

		htmlBytes, err := convertDicomToHtml(lg, path, repFormat)
		if err != nil {
			return nil, repFormat, err
		}

		if accept == "text/html" {
			return htmlBytes, repFormat, nil
		} else if accept == "application/pdf" {
			// write html report to disc
			err = os.WriteFile(path+".html", htmlBytes, 0600)
			if err != nil {
				lg.WithError(err).Error("Get report write html failed")
				return nil, repFormat, err
			}
			stdouterr, err := convertHtmlToPdf(lg, path)
			if err != nil {
				lg.WithField("stdouterr", string(stdouterr)).WithError(err).Error("Get report wkhtmltopdf failed")
				return nil, repFormat, err
			}

			pdfBytes, err := os.ReadFile(path + ".pdf") // #nosec G304
			if err != nil {
				lg.WithError(err).Error("error reading report pdf file after convertHtmlToPdf")
				return nil, repFormat, err
			}
			return pdfBytes, repFormat, nil
		} else {
			// write html report to disc
			err = os.WriteFile(path+".html", htmlBytes, 0600)
			if err != nil {
				lg.WithError(err).Error("Get report write html failed")
				return nil, repFormat, err
			}
			if repFormat == dicomutils.CareStreamSR {
				stdouterr, err := convHtmlToPdfToImage(lg, path)
				if err != nil {
					lg.WithField("stdouterr", string(stdouterr)).WithError(err).Error("Get report convert failed")
					return nil, repFormat, err
				}
			} else {

				stdouterr, err := convertHtmlToPng(lg, path)
				if err != nil {
					lg.WithField("stdouterr", string(stdouterr)).WithError(err).Error("Get report wkhtmltoimage failed")
					return nil, repFormat, err
				}
			}
			pngBytes, err := os.ReadFile(path + ".png") // #nosec G304
			if err != nil {
				lg.WithError(err).Error("error reading report png file")
				return nil, repFormat, err
			}
			return pngBytes, repFormat, err
		}
	} else {
		repFormat = dicomutils.Other
		_, err := convertDicomToPng(lg, path)
		if err != nil {
			return nil, repFormat, err
		}
		pngBytes, err := os.ReadFile(path + ".png") // #nosec G304
		if err != nil {
			lg.WithError(err).Error("error reading report pdf file after convertDicomToPng")
			return nil, repFormat, err
		}
		return pngBytes, repFormat, err

	}
}

// necessary for avoiding issues when converting reports from CareStream PACS
func convHtmlToPdfToImage(lg *logrus.Entry, path string) ([]byte, error) {
	stdouterr, err := cmdCombinedOutput(
		lg,
		"xvfb-run",
		[]string{"-a", "wkhtmltopdf", path + ".html", path + ".pdf"},
	)
	if err != nil {
		lg.WithField("stdouterr", string(stdouterr)).
			WithError(err).
			Error("Get report wkhtmltopdf failed")
		return nil, err
	}
	stdouterr, err = cmdCombinedOutput(
		lg,
		"convert",
		[]string{"-density", "150", "-append", path + ".pdf", path + ".png"},
	)
	return stdouterr, err
}

// convert pdf at {path} to html at path.html
func convertPdfToHtml(lg *logrus.Entry, path string) ([]byte, error) {
	stdouterr, err := cmdCombinedOutput(
		lg,
		"pdf2htmlEX",
		[]string{"--optimize-text", "1", path + ".pdf", path + ".html"},
	)
	if err != nil {
		lg.WithField("stdouterr", string(stdouterr)).
			WithError(err).
			Error("Get report pdftohtml failed")
		return nil, err
	}

	htmlbytes, err := os.ReadFile(path + ".html") // #nosec G304
	if err != nil {
		lg.WithError(err).Error("could not open html generated file")
		return nil, err
	}

	//replace all occurrences of the unicode character `\uE003` with this special
	//html code for an invisible space character that is supported by angular, since
	//`\uE003` appears as squares and is present in every HDIRS converted report
	htmlbytes = spaceRe.ReplaceAllLiteral(htmlbytes, []byte("&#8287;"))
	return []byte(htmlbytes), err
}

func cmdCombinedOutput(lg *logrus.Entry, path string, args []string) ([]byte, error) {
	cmd := exec.Command(path, args...)
	stdouterr, err := cmd.CombinedOutput()
	if err != nil {
		return stdouterr, err
	}

	if stdouterr != nil && len(stdouterr) > 0 {
		lg.
			WithField("cmd", cmd.Path).
			Error("cmdCombinedOutput did not return error but logged something")
	}

	return nil, nil
}

func cmdWithOutput(lg *logrus.Entry, path string, args []string) ([]byte, error) {
	cmd := exec.Command(path, args...)
	out, err := cmd.Output()
	if err != nil {
		return out, err
	}

	if len(out) > 0 {
		lg.WithField("cmd", cmd.Path).
			Error("cmdWithOutput did not return error but logged something")
	}

	return out, nil
}

func parseCarestreamSRToHTML(path string) ([]byte, error) {
	dataSet, err := dicom.ParseFile(path, nil)
	if err != nil {
		return []byte{}, err
	}
	csHtmlElement, err := dataSet.FindElementByTag(tag.Tag{Group: 0x07a3, Element: 0x1047}) //custom tag carestream uses
	if err != nil {
		logrus.WithError(err).Error("could not fetch html")
		return []byte{}, err
	}

	var htmlBytes []byte
	v, ok := csHtmlElement.Value.GetValue().([]byte)
	if ok {
		htmlBytes = v
	} else {
		return []byte{}, nil
	}

	return sanitizeCarestreamHTML(htmlBytes), nil

}

// these reports have notoriously broken html...fix some known problem cases
func sanitizeCarestreamHTML(inputHTML []byte) []byte {

	htmlreg, _ := regexp.Compile("(<html)(.|[\r\n])*(<\\/html>)")
	cleanHTML := htmlreg.Find(inputHTML)

	cleanHTML = []byte(stripKeyImagesFromCSHTML(string(cleanHTML)))

	//span followed by 0+ spaces then an equals sign
	spanReg, _ := regexp.Compile("<span( )*=")
	cleanHTML = spanReg.ReplaceAll(cleanHTML, []byte("<span "))

	//p followed by 0+ spaces then an equals sign
	parReg, _ := regexp.Compile("<p( )*=")
	cleanHTML = parReg.ReplaceAll(cleanHTML, []byte("<p "))

	//= on the end of line
	lineReg, _ := regexp.Compile("=\r?\n|\r")
	cleanHTML = lineReg.ReplaceAll(cleanHTML, []byte(""))

	//3D before "
	threeDreg, _ := regexp.Compile(`3D"`)
	cleanHTML = threeDreg.ReplaceAll(cleanHTML, []byte(`"`))

	//broken span tags at the beginning of lines
	brokenSpanReg, _ := regexp.Compile("^an>")
	cleanHTML = brokenSpanReg.ReplaceAll(cleanHTML, []byte(""))

	//these tags that just show up sometimes
	tagReg, _ := regexp.Compile("<span.*=E2=80=A2.*span>")
	cleanHTML = tagReg.ReplaceAll(cleanHTML, []byte(""))

	tag2Reg, _ := regexp.Compile("=C2=A0")
	cleanHTML = tag2Reg.ReplaceAll(cleanHTML, []byte(""))

	//from the original js script that did this parsing:
	stripReg, _ := regexp.Compile("[^\x00-\x7F]")
	cleanHTML = stripReg.ReplaceAll(cleanHTML, []byte(""))

	return cleanHTML
}

func stripKeyImagesFromCSHTML(htmlStr string) string {
	// strip out key images
	// search for img src with "key_images\\image1.jpg
	indexOfKeyImages := strings.Index(htmlStr, "key_images\\image1.jpg")
	if indexOfKeyImages != -1 {
		// search backwards for the start of the "<img" tag, starting from where we found "key_images\\image1.jpg"
		var indexOfStartImg = strings.LastIndex(htmlStr[:indexOfKeyImages], "<img")
		if indexOfStartImg != -1 {
			// search forwards for the end of the "<img" tag, as given by "/>", from where we found "key_images\\image1.jpg"
			var indexOfEndImg = strings.Index(htmlStr[indexOfKeyImages:], "/>")
			if indexOfEndImg != -1 {
				// remove the <img tag altogether
				firsthalf := htmlStr[:indexOfStartImg]
				secondhalf := htmlStr[indexOfEndImg+indexOfKeyImages+2:]
				return firsthalf + secondhalf
			}
		}
	}
	return htmlStr
}
