package definitions

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/reportprocessor/pkg/mysql/helpers"
)

type DefStore interface {
	GetDefinitions(context.Context, []string, int) (map[string]string, []string, error)
	GetImages(context.Context, []string, int) (map[string]ImageInfo, error)
	GetSubdictionaryId(context.Context, string) int
	GetOrganvizTerms(context.Context, string) ([]string, error)
}

type MysqlDefStore struct {
	db           *sql.DB
	maxBatchSize int
}

type ImageInfo struct {
	ImageUrl      string `json:"image_url"`
	SourceName    string `json:"source_name"`
	Modified      bool   `json:"modified"`
	SourceAnchor  string `json:"source_anchor"`
	LicenseAnchor string `json:"license_anchor"`
}

func NewMysqlDefStore(database *sql.DB) DefStore {
	return &MysqlDefStore{db: database, maxBatchSize: 50}
}

func (ds *MysqlDefStore) GetDefinitions(
	ctx context.Context,
	terms []string,
	subdictId int,
) (map[string]string, []string, error) {
	//unlikely for this to get too huge, but batch up just in case
	totalTerms := len(terms)
	termMap := make(map[string]string)
	organViz := []string{}
	organVizMap := make(map[string]string)
	for start := 0; start < totalTerms; start += ds.maxBatchSize {
		var end int
		if totalTerms < start+ds.maxBatchSize {
			end = totalTerms
		} else {
			end = start + ds.maxBatchSize
		}
		rows, err := doDefinitionsQuery(ctx, ds.db, terms[start:end], subdictId)
		if err != nil {
			logutils.CtxLogger(ctx).WithError(err).Error("error getting definitions")
			return nil, []string{}, err
		}
		defer rows.Close()
		for rows.Next() {
			var term, def string
			var is_organviz_organ bool
			err := rows.Scan(&term, &def, &is_organviz_organ)
			if err != nil {
				logutils.CtxLogger(ctx).WithError(err).Error("error reading definitions")
				return nil, []string{}, err
			}
			term = strings.ToLower(term)
			// prevent duplication of organ viz terms
			_, ok := organVizMap[term]
			if is_organviz_organ && !ok {
				organViz = append(organViz, term)
				organVizMap[term] = ""
			}
			// since we're ordering by subdict_id, subdictonary terms overwrite the base terms
			termMap[term] = def
		}
	}
	return termMap, organViz, nil
}

func doDefinitionsQuery(ctx context.Context, db *sql.DB, terms []string, subdictId int) (*sql.Rows, error) {
	args := helpers.CreateSqlArgString(len(terms))
	queryArgs := helpers.ConvertToInterfaceSlice(terms)
	queryArgs = append(queryArgs, subdictId)

	return db.QueryContext(
		ctx,
		fmt.Sprintf(`SELECT term, def, is_organviz_organ from term_definitions 
							WHERE term in (%s) AND subdictionary_id IN (0, ?) 
							ORDER BY term, subdictionary_id`, args),
		queryArgs...)
}

func (ds *MysqlDefStore) GetImages(
	ctx context.Context, terms []string, subdictId int,
) (map[string]ImageInfo, error) {
	imageMap := make(map[string]ImageInfo)

	rows, err := doImagesQuery(ctx, ds.db, terms, subdictId)
	if err != nil {
		logutils.CtxLogger(ctx).WithError(err).Error("error getting images")
		return nil, err
	}
	defer rows.Close()
	for rows.Next() {
		var term, imageLink, sourceName, sourceAnchor, licenseAnchor string
		var modified sql.NullBool
		err := rows.Scan(&term, &imageLink, &sourceName, &modified, &sourceAnchor, &licenseAnchor)
		if err != nil {
			logutils.CtxLogger(ctx).WithError(err).Error("error reading images")
			return nil, err
		}
		imageMap[term] = ImageInfo{imageLink, sourceName, modified.Bool, sourceAnchor, licenseAnchor}
	}
	return imageMap, nil
}

func doImagesQuery(ctx context.Context, db *sql.DB, terms []string, subdictId int) (*sql.Rows, error) {
	args := helpers.CreateSqlArgString(len(terms))
	queryArgs := helpers.ConvertToInterfaceSlice(terms)
	queryArgs = append(queryArgs, subdictId)
	return db.QueryContext(
		ctx,
		fmt.Sprintf(
			`SELECT td.term, td.image_link, ic.source_name, ic.modified, ic.source_anchor, ic.license_anchor 
			FROM reportprocessor.term_definitions td JOIN reportprocessor.report_image_citations ic ON td.citation_id = ic.citation_id
			WHERE term in (%s) AND subdictionary_id IN (0, ?) 
			ORDER BY term, subdictionary_id;`, args),
		queryArgs...,
	)
}

func (ds *MysqlDefStore) GetSubdictionaryId(
	ctx context.Context, subdictionary string,
) int {
	if subdictionary == "" {
		return 0
	} else {
		var subdictionaryId int
		err := ds.db.QueryRow("SELECT id FROM subdictionaries WHERE name = ?", subdictionary).
			Scan(&subdictionaryId)

		if err != nil {
			if err == sql.ErrNoRows {
				logutils.CtxLogger(ctx).WithError(err).Error("invalid subdictionary query param")
			} else {
				logutils.CtxLogger(ctx).WithError(err).Error("error reading subdictionary")
			}
			return 0
		}
		return subdictionaryId
	}
}

func (ds *MysqlDefStore) GetOrganvizTerms(ctx context.Context, model string) ([]string, error) {
	var query string
	args := make([]interface{}, 0)
	// Default to "ct_abd" if model is not provided
	if model == "" {
		model = "ct_abd"
	}
	if model == "all" {
		query = `SELECT term 
        FROM term_definitions 
        WHERE is_organviz_organ = 1`
	} else {
		query = `SELECT td.term 
        FROM term_definitions td 
        JOIN organviz_term_modality_mappings om ON td.term = om.term 
        WHERE td.is_organviz_organ = 1 AND om.active_for_model = ?`
		args = append(args, model)
	}

	rows, err := ds.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed executing SQL query: %w", err)
	}
	defer rows.Close()

	var organVizTerms []string
	for rows.Next() {
		var t string
		err := rows.Scan(&t)
		if err != nil {
			return nil, fmt.Errorf("failed scanning row: %w", err)
		}
		organVizTerms = append(organVizTerms, strings.ToLower(t))
	}

	return organVizTerms, nil
}
