package definitions

import (
	"context"
	"fmt"

	"gitlab.com/pockethealth/reportprocessor/pkg/insights/insightssvc"
)

type MockDefStore struct {
	organvizTerms []string
}

func (md *MockDefStore) GetDefinitions(
	ctx context.Context,
	terms []string,
	subdictId int,
) (map[string]string, []string, error) {
	termMap := make(map[string]string, len(terms))
	for i := range terms {
		termMap[terms[i]] = fmt.Sprintf("definition: %d", i)
	}
	return termMap, []string{terms[0]}, nil
}

func (md *MockDefStore) GetImages(
	ctx context.Context,
	terms []string,
	subdictId int,
) (map[string]ImageInfo, error) {
	imageMap := make(map[string]ImageInfo, len(terms))
	for i := range terms {
		imageMap[terms[i]] = ImageInfo{
			ImageUrl:      fmt.Sprintf("image url: %d", i),
			SourceName:    fmt.Sprintf("source name: %d", i),
			Modified:      true,
			SourceAnchor:  fmt.Sprintf("source anchor: %d", i),
			LicenseAnchor: fmt.Sprintf("license anchor: %d", i),
		}
	}

	return imageMap, nil
}

func (md *MockDefStore) GetSubdictionaryId(ctx context.Context, subdictionary string) int {
	if subdictionary == "breast" {
		return 1
	}
	return 0
}

func (md *MockDefStore) GetOrganvizTerms(ctx context.Context, model string) ([]string, error) {
	// Default to "ct_abd" if model is empty
	if model == "" {
		model = "ct_abd"
	}

	if insightssvc.IsValidModel(model) {
		return md.organvizTerms, nil
	} else {
		return nil, fmt.Errorf("invalid model: %s", model)
	}
}

func (md *MockDefStore) SetOrganvizTerms(organs ...string) {
	md.organvizTerms = organs
}
