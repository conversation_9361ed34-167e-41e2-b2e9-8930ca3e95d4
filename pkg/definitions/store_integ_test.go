//go:build integration
// +build integration

package definitions

import (
	"context"
	"database/sql"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/reportprocessor/pkg/testutils"
)

func TestGetDefinitionsAndImages(t *testing.T) {
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	logrus.SetLevel(logrus.DebugLevel)
	ctx := context.Background()

	db, err := sql.Open("mysql", cfg.SqlConnString)
	if err != nil {
		t.Fatal("can't connect to mysql", err)
	}

	tx, err := db.BeginTx(ctx, nil)
	require.NoError(t, err)
	defer tx.Rollback()

	// insert an entry that will overwrite a base term
	_, err = tx.Exec(
		"INSERT INTO term_definitions VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
		"heart",
		"testing overwrite entry",
		"fakewebsite.com",
		1,
		0,
		1,
		nil,
		nil,
	)
	require.NoError(t, err, "could not add db entry")
	tx.Commit()
	t.Cleanup(func() {
		db.Query("DELETE FROM term_definitions WHERE def = ?", "testing overwrite entry")
	})

	testTerms := []string{"aorta", "heart"}
	ds := MysqlDefStore{db: db, maxBatchSize: 50}

	t.Run("when calling GetDefinitions", func(t *testing.T) {
		t.Run("when getting base terms, nothing should be overwritten", func(t *testing.T) {
			expectedDefs := map[string]string{
				"aorta": "A large blood vessel that carries blood from the heart to other parts of the body.",
				"heart": "A muscular organ in the centre of the chest. It pumps blood throughout the body.",
			}
			actualDefs, _, err := ds.GetDefinitions(ctx, testTerms, 0)
			require.NoError(t, err, "could not get definitions")

			assert.Len(t, actualDefs, 2)
			assert.Equal(t, expectedDefs["aorta"], actualDefs["aorta"])
			assert.Equal(t, expectedDefs["heart"], actualDefs["heart"])
		})
		t.Run(
			"when getting subdict terms, subdictionary terms should overwrite base",
			func(t *testing.T) {
				expectedDefs := map[string]string{
					"aorta": "A large blood vessel that carries blood from the heart to other parts of the body.",
					"heart": "testing overwrite entry",
				}
				actualDefs, _, err := ds.GetDefinitions(ctx, testTerms, 1)
				require.NoError(t, err, "could not get definitions")

				assert.Len(t, actualDefs, 2)
				assert.Equal(t, expectedDefs["aorta"], actualDefs["aorta"])
				assert.Equal(t, expectedDefs["heart"], actualDefs["heart"])
			},
		)
	})

	t.Run("when calling GetImages", func(t *testing.T) {
		t.Run("when getting base images, nothing should be overwritten", func(t *testing.T) {
			expectedImages := map[string]ImageInfo{
				"aorta": {
					"assets.pocket.health/rr_so926Cn_",
					"PocketHealth",
					false,
					"",
					"",
				},
				"heart": {
					"assets.pocket.health/rr_RkAat9Hd",
					"PocketHealth",
					false,
					"",
					"",
				},
			}
			actualImages, err := ds.GetImages(ctx, testTerms, 0)
			require.NoError(t, err, "could not get definitions")

			assert.Len(t, actualImages, 2)
			assert.Equal(t, expectedImages["aorta"], actualImages["aorta"])
			assert.Equal(t, expectedImages["heart"], actualImages["heart"])
		})
		t.Run(
			"when getting subdict terms, subdictionary terms should overwrite base",
			func(t *testing.T) {
				expectedImages := map[string]ImageInfo{
					"aorta": {
						"assets.pocket.health/rr_so926Cn_",
						"PocketHealth",
						false,
						"",
						"",
					},
					"heart": {
						"fakewebsite.com",
						"Servier",
						false,
						"<a href=\"https://smart.servier.com/\" target=\"_blank\">Servier Medical Art</a>",
						"<a href=\"https://creativecommons.org/licenses/by/4.0/\" target=\"_blank\">CC BY 4.0</a>",
					},
				}
				actualImages, err := ds.GetImages(ctx, testTerms, 1)
				require.NoError(t, err, "could not get definitions")

				assert.Len(t, actualImages, 2)
				assert.Equal(t, expectedImages["aorta"], actualImages["aorta"])
				assert.Equal(t, expectedImages["heart"], actualImages["heart"])
			},
		)
	})

}
