//go:build integration
// +build integration

package reports

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"testing"

	"golang.org/x/oauth2"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/reportprocessor/pkg/auth"
	"gitlab.com/pockethealth/reportprocessor/pkg/blob"
	"gitlab.com/pockethealth/reportprocessor/pkg/definitions"
	"gitlab.com/pockethealth/reportprocessor/pkg/insights"
	"gitlab.com/pockethealth/reportprocessor/pkg/insights/insightssvc"
	"gitlab.com/pockethealth/reportprocessor/pkg/rpApi"
	"gitlab.com/pockethealth/reportprocessor/pkg/tagger"
	"gitlab.com/pockethealth/reportprocessor/pkg/testutils"
)

// define authStore
type TestAuthStore struct{}

func (tas *TestAuthStore) GetAPIUser(name string) (auth.APIUser, error) {
	return auth.APIUser{
		KeyHash: "fd222b13f7c67e748a547289173c839b15662171515cac01b3257a7dee4bffa7",
		Role:    auth.Read | auth.Write,
	}, nil
}

func openSampleFile(t *testing.T, filename string) *os.File {
	_, b, _, _ := runtime.Caller(0)
	basepath := filepath.Join(filepath.Dir(b), "../..")
	fp, err := os.Open(basepath + "/apptest/sampledicom/" + filename)
	if err != nil {
		t.Fatal("sample file open failed", err)
	}
	return fp
}

func newAuthedRequest(t *testing.T, ctx context.Context, method string, requestUri string) *http.Request {
	req, err := http.NewRequestWithContext(ctx, method, requestUri, nil)
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Add(
		"Authorization",
		"Basic Y29yZVI6TUpSWVNkYWJrWnAyMnpITDZnQ25vU2Y0UjE4am1RMHRlRWRqNTdSYm5rVXl6a3htZ1hXeHFWMnJYTE9PaWtQag==",
	)
	return req
}

func setup(t *testing.T, a auth.Authenticator) *mux.Router {
	testutils.WDtoProjDir(t)
	t.Cleanup(func() {
		os.RemoveAll("vault")
	})
	//create tmp directory for temporary files during file conversion
	if _, err := os.Stat("vault"); os.IsNotExist(err) {
		mkdirErr := os.Mkdir("vault", 0700)
		if mkdirErr != nil {
			t.Fatalf("failed to set up temp dirs, %s", mkdirErr)
		}
	}
	if _, err := os.Stat("vault/tmp"); os.IsNotExist(err) {
		mkdirErr := os.Mkdir("vault/tmp", 0700)
		if mkdirErr != nil {
			t.Fatalf("failed to set up temp dirs, %s", mkdirErr)
		}
	}

	testhtml, err := os.Open("apptest/sampledicom/encapsulatedPDF")
	if err != nil {
		t.Fatalf("could not read test file:%v", err)
	}
	t.Cleanup(func() {
		testhtml.Close()
	})
	termList := []string{"this is a", "report", "configurable period", "this", "send"}
	organvizTerm := []string{"this is a", "spleen"}
	tag := &tagger.Tagger{}
	tag.Build(termList, organvizTerm)
	srv := NewReportsApiService(&blob.MockBlobStore{File: testhtml, TestHandle: t}, tag, &definitions.MockDefStore{})

	ctrl := NewReportsApiController(srv, nil, a)

	return rpApi.NewRouter(ctrl)
}

func TestGetReportAuth(t *testing.T) {
	a := auth.Authenticator{
		APIUsers: map[string]auth.APIUser{
			"core": {
				KeyHash: "fd222b13f7c67e748a547289173c839b15662171515cac01b3257a7dee4bffa7",
				Role:    auth.Write,
			},
			"coreR": {
				KeyHash: "fd222b13f7c67e748a547289173c839b15662171515cac01b3257a7dee4bffa7",
				Role:    auth.Read,
			}},
	}
	router := setup(t, a)
	ctx := context.WithValue(context.Background(), logutils.CorrelationIdContextKey, "somecorid")

	t.Run("no auth", func(t *testing.T) {
		req, err := http.NewRequest("GET", "/v1/reports/123", nil)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusUnauthorized
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
	t.Run("unauthorized", func(t *testing.T) {
		req, err := http.NewRequestWithContext(ctx, "GET", "/v1/reports/123", nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Add(
			"Authorization",
			"Basic Y29yZTpNSlJZU2RhYmtacDIyekhMNmdDbm9TZjRSMThqbVEwdGVFZGo1N1JibmtVeXpreG1nWFd4cVYyclhMT09pa1Bq",
		)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusUnauthorized
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
	t.Run("authorized", func(t *testing.T) {
		req := newAuthedRequest(t, ctx, "GET", "/v1/reports/123")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
	})
}

func TestGetReportRequest(t *testing.T) {
	a := auth.Authenticator{
		AuthStore: &TestAuthStore{},
		APIUsers: map[string]auth.APIUser{
			"core": {
				KeyHash: "fd222b13f7c67e748a547289173c839b15662171515cac01b3257a7dee4bffa7",
				Role:    auth.Read,
			}},
	}
	ctx := context.WithValue(context.Background(), logutils.CorrelationIdContextKey, "somecorid")
	router := setup(t, a)
	cases := []struct {
		name           string
		accept         string
		expectedStatus int
	}{
		{
			"png",
			"image/png",
			http.StatusOK,
		},
		{
			"pdf",
			"application/pdf",
			http.StatusOK,
		},
		{
			"html",
			"text/html",
			http.StatusOK,
		},
		{
			"blank",
			"",
			http.StatusOK,
		},
		{
			"bad type",
			"application/json",
			http.StatusBadRequest,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			req := newAuthedRequest(t, ctx, "GET", "/v1/reports/123")
			req.Header.Add("Accept", c.accept)
			rr := httptest.NewRecorder()
			router.ServeHTTP(rr, req)

			want := c.expectedStatus
			if status := rr.Code; status != want {
				t.Errorf("handler returned wrong status code: got %v want %v",
					status, want)
			}
		})
	}
}

func TestGetReportTagged(t *testing.T) {

	a := auth.Authenticator{
		AuthStore: &TestAuthStore{},
		APIUsers: map[string]auth.APIUser{
			"core": {
				KeyHash: "fd222b13f7c67e748a547289173c839b15662171515cac01b3257a7dee4bffa7",
				Role:    auth.Read,
			}},
	}
	router := setup(t, a)

	t.Run("encap pdf, two terms", func(t *testing.T) {

		ctx := context.WithValue(
			context.Background(),
			logutils.CorrelationIdContextKey,
			"somecorid",
		)
		req := newAuthedRequest(t, ctx, "GET", "/v1/reports/123/taggedhtml")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
		//TODO: validate HTML
		var th rpApi.TaggedHTML
		err := json.Unmarshal(rr.Body.Bytes(), &th)
		if err != nil {
			t.Fatalf("could not unmarshal response: %v", err)
		}
		expectedTerms := []string{"This is a", "report"}
		if len(th.Defs) != len(expectedTerms) {
			t.Fatalf(
				"expected definitions to be populated with %d terms, but got %d",
				len(expectedTerms),
				len(th.Defs),
			)
		}
		for i := range expectedTerms {
			if _, ok := th.Defs[expectedTerms[i]]; !ok {
				t.Fatalf("expected defs to contain %s, %v", expectedTerms[i], th.Defs)
			}
		}
		// check if images are correct
		if len(th.Images) != len(expectedTerms) {
			t.Fatalf(
				"expected images to be populated with %d terms, got %d",
				len(expectedTerms),
				len(th.Defs),
			)
		}
		for i := range expectedTerms {
			if _, ok := th.Images[expectedTerms[i]]; !ok {
				t.Fatalf("expected imgs to contain %s, %v", expectedTerms[i], th.Defs)
			}
			if th.Images[expectedTerms[i]].ImageUrl == "" {
				t.Fatalf("expected images to contain url")
			}
			if th.Images[expectedTerms[i]].SourceName == "" {
				t.Fatalf("expected images to source name")
			}
			if th.Images[expectedTerms[i]].Modified == false {
				t.Fatalf("expected images to be true")
			}
			if th.Images[expectedTerms[i]].SourceAnchor == "" {
				t.Fatalf("expected images to contain source anchor")
			}
			if th.Images[expectedTerms[i]].LicenseAnchor == "" {
				t.Fatalf("expected images to contain license anchor")
			}
		}

		html := string(th.Report)
		//expect two instances of the "phdefinition" substring
		spanCount := strings.Count(html, "phdefinition")
		if spanCount != len(expectedTerms) {
			t.Fatalf("expected %d terms to be tagged, got %d", len(expectedTerms), spanCount)
		}
	})

	t.Run("encap pdf, two terms, 1 organ viz term", func(t *testing.T) {

		ctx := context.WithValue(
			context.Background(),
			logutils.CorrelationIdContextKey,
			"somecorid",
		)
		req := newAuthedRequest(t, ctx, "GET", "/v1/reports/123/taggedhtml")
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		want := http.StatusOK
		if status := rr.Code; status != want {
			t.Errorf("handler returned wrong status code: got %v want %v",
				status, want)
		}
		//TODO: validate HTML
		var th rpApi.TaggedHTML
		err := json.Unmarshal(rr.Body.Bytes(), &th)
		if err != nil {
			t.Fatalf("could not unmarshal response: %v", err)
		}
		expectedTerms := []string{"This is a", "report"}
		if len(th.Defs) != len(expectedTerms) {
			t.Fatalf(
				"expected definitions to be populated with %d terms, but got %d",
				len(expectedTerms),
				len(th.Defs),
			)
		}
		for i := range expectedTerms {
			if _, ok := th.Defs[expectedTerms[i]]; !ok {
				t.Fatalf("expected defs to contain %s, %v", expectedTerms[i], th.Defs)
			}
		}
		// check if images are correct
		if len(th.Images) != len(expectedTerms) {
			t.Fatalf(
				"expected images to be populated with %d terms, got %d",
				len(expectedTerms),
				len(th.Images),
			)
		}

		expectedOrganVizTerms := []string{"This is a"}
		// check if organ viz terms are correct
		if len(th.OrganVizTerms) != len(expectedOrganVizTerms) {
			t.Fatalf(
				"expected organ viz terms to be populated with %d terms, got %d",
				len(expectedOrganVizTerms),
				len(th.OrganVizTerms),
			)
		}
		for i := range expectedTerms {
			if _, ok := th.Images[expectedTerms[i]]; !ok {
				t.Fatalf("expected imgs to contain %s, %v", expectedTerms[i], th.Defs)
			}
			if th.Images[expectedTerms[i]].ImageUrl == "" {
				t.Fatalf("expected images to contain url")
			}
			if th.Images[expectedTerms[i]].SourceName == "" {
				t.Fatalf("expected images to source name")
			}
			if th.Images[expectedTerms[i]].Modified == false {
				t.Fatalf("expected images to be true")
			}
			if th.Images[expectedTerms[i]].SourceAnchor == "" {
				t.Fatalf("expected images to contain source anchor")
			}
			if th.Images[expectedTerms[i]].LicenseAnchor == "" {
				t.Fatalf("expected images to contain license anchor")
			}
		}

		html := string(th.Report)
		//expect two instances of the "phdefinition" substring
		spanCount := strings.Count(html, "phdefinition")
		organvizCount := strings.Count(html, "organvizTerm")
		if spanCount != len(expectedTerms) {
			t.Fatalf("expected %d terms to be tagged, got %d", len(expectedTerms), spanCount)
		}
		if organvizCount != len(expectedOrganVizTerms) {
			t.Fatalf("expected %d terms to be tagged, got %d", len(expectedOrganVizTerms), organvizCount)
		}
	})
}

func TestGetReportInsights(t *testing.T) {
	svc := insights.InsightsApiService{
		InsightsJobDeps: insights.InsightsJobDeps{
			ObjsBlob:     nil,
			InsightsBlob: nil,
			InsightsSvc:  nil,
		},
	}
	a := auth.Authenticator{
		AuthStore: &TestAuthStore{},
		APIUsers: map[string]auth.APIUser{
			"core": {
				KeyHash: "fd222b13f7c67e748a547289173c839b15662171515cac01b3257a7dee4bffa7",
				Role:    auth.Read,
			}},
	}
	ctrl := NewReportsApiController(nil, &svc, a)

	router := rpApi.NewRouter(ctrl)
	ctx := context.WithValue(
		context.Background(),
		logutils.CorrelationIdContextKey,
		"somecorid",
	)

	cases := []struct {
		name     string
		setup    func()
		teardown func()
	}{
		{name: "get followup 200 raw text exists", setup: func() {
			svc.InsightsBlob = &blob.MockBlobStore{CanAccessVal: true}
		}, teardown: func() {
			svc.InsightsBlob = nil
		}},
		{name: "get followup 200 raw text not extracted yet", setup: func() {
			fp := openSampleFile(t, "containsFollowup.dcm")
			t.Cleanup(func() { fp.Close() })

			svc.ObjsBlob = &blob.MockBlobStore{
				File: fp,
			}
			svc.InsightsBlob = &blob.MockBlobStore{CanAccessVal: false}
		}, teardown: func() {
			svc.InsightsBlob = nil
		}},
	}

	for _, testCase := range cases {
		t.Run(testCase.name, func(t *testing.T) {
			testCase.setup()
			// test setup
			rr := httptest.NewRecorder()
			mock_reportinsights := insightssvc.NewTestServer()

			tokenSrc := testutils.NewMockTokenSource()
			client := oauth2.NewClient(context.Background(), tokenSrc)

			svc.InsightsSvc = insightssvc.S2SNewClient(mock_reportinsights.URL, "", httpclient.NewHTTPClient(client, nil))
			t.Cleanup(func() {
				svc.InsightsSvc = nil
				mock_reportinsights.Close()
				testCase.teardown()
			})

			req := newAuthedRequest(t, ctx, "GET", "/v1/reports/followup-exists/insights/followup")
			router.ServeHTTP(rr, req)

			if rr.Code != http.StatusOK {
				t.Fatalf("expected status %d but got %d", http.StatusOK, rr.Code)
			}
			var followup insightssvc.FollowupResponse
			err := json.Unmarshal(rr.Body.Bytes(), &followup)
			if err != nil {
				t.Fatal("error unmarshaling response", rr.Body.String(), err)
			}
			if !followup.Exists {
				t.Fatal("expected followup.Exists to be true but was false")
			}
			if len(followup.Occurrences) != 1 {
				t.Fatalf("expected length %d but got %d", 1, len(followup.Occurrences))
			}
		})
	}

	t.Run("bulk endpoint returns a correctly formatted response", func(t *testing.T) {
		// set up insightssvc mock to return followups for report-1 and report-2
		r1followup := insightssvc.FollowupResponse{
			Exists: true,
			Occurrences: []insightssvc.FollowUp{{
				Context:       "wibbly-wobbly",
				Category:      "some-category",
				MinTimeFrame:  2,
				MaxTimeFrame:  3,
				TimeFrameUnit: "eons",
			}},
		}
		r2followup := insightssvc.FollowupResponse{
			Exists: true,
			Occurrences: []insightssvc.FollowUp{{
				Context:       "timey-wimey",
				Category:      "some-other-category",
				MinTimeFrame:  5,
				MaxTimeFrame:  7,
				TimeFrameUnit: "months",
			}},
		}
		mock_reportinsights := testutils.NewMockHttpServer(t, map[string]insightssvc.FollowupBatchResponse{
			"/v1/followup/bulk": {{
				ReportId:  "report-1",
				Followups: r1followup,
			}, {
				ReportId:  "report-2",
				Followups: r2followup,
			}},
		})

		tokenSrc := testutils.NewMockTokenSource()
		client := oauth2.NewClient(context.Background(), tokenSrc)

		svc.InsightsSvc = insightssvc.S2SNewClient(mock_reportinsights.URL, "", httpclient.NewHTTPClient(client, nil))
		t.Cleanup(func() {
			svc.InsightsSvc = nil
			mock_reportinsights.Close()
		})
		// done setting up insightssvc

		// send request for report-1 and report-2 to insightssvc mock
		var bodyBuffer bytes.Buffer
		err := json.NewEncoder(&bodyBuffer).Encode([]string{"report-1", "report-2"})
		require.NoError(t, err)

		req := newAuthedRequest(t, ctx, "POST", "/v1/reports/insights/followups")
		req.Body = io.NopCloser(&bodyBuffer)

		resp := httptest.NewRecorder()
		router.ServeHTTP(resp, req)
		// done sending request

		// assert response is formatted the way we want
		expectedResponse := map[string]insightssvc.FollowupResponse{
			"report-1": r1followup,
			"report-2": r2followup,
		}
		var actual map[string]insightssvc.FollowupResponse
		err = json.NewDecoder(resp.Body).Decode(&actual)
		require.NoError(t, err)
		assert.Equal(t, expectedResponse, actual)
	})

	cases = []struct {
		name     string
		setup    func()
		teardown func()
	}{
		{name: "get questions 200 raw text exists", setup: func() {
			svc.InsightsBlob = &blob.MockBlobStore{CanAccessVal: true}
		}, teardown: func() {
			svc.InsightsBlob = nil
		}},
		{name: "get questions 200 raw text not extracted yet", setup: func() {
			fp := openSampleFile(t, "containsFollowup.dcm")
			t.Cleanup(func() { fp.Close() })

			svc.ObjsBlob = &blob.MockBlobStore{
				File: fp,
			}
			svc.InsightsBlob = &blob.MockBlobStore{CanAccessVal: false}
		}, teardown: func() {
			svc.InsightsBlob = nil
		}},
	}

	for _, testCase := range cases {
		t.Run(testCase.name, func(t *testing.T) {
			testCase.setup()
			// test setup
			mock_reportinsights := insightssvc.NewTestServer()
			tokenSrc := testutils.NewMockTokenSource()
			client := oauth2.NewClient(context.Background(), tokenSrc)

			svc.InsightsSvc = insightssvc.S2SNewClient(mock_reportinsights.URL, "", httpclient.NewHTTPClient(client, nil))
			t.Cleanup(func() {
				svc.InsightsSvc = nil
				mock_reportinsights.Close()
				testCase.teardown()
			})
			rr := httptest.NewRecorder()
			req := newAuthedRequest(t, ctx, "GET", "/v1/reports/questions-exist/insights/questions")
			router.ServeHTTP(rr, req)

			if rr.Code != http.StatusOK {
				t.Fatalf("expected status %d but got %d", http.StatusOK, rr.Code)
			}
			var resp insightssvc.QuestionResponse

			err := json.Unmarshal([]byte(rr.Body.String()), &resp)
			if err != nil {
				t.Fatal("error unmarshaling response", err, rr.Body.String())
			}
			if len(resp.Questions) != 2 {
				t.Fatalf("expected length %d but got %d", 2, len(resp.Questions))
			}
		})
	}
}
