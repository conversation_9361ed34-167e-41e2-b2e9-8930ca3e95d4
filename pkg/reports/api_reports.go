/*
 * reportprocessor
 *
 * API for PocketHealth report processing
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package reports

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/reportprocessor/pkg/auth"
	"gitlab.com/pockethealth/reportprocessor/pkg/insights"
	"gitlab.com/pockethealth/reportprocessor/pkg/insights/insightssvc"
	"gitlab.com/pockethealth/reportprocessor/pkg/rpApi"
)

// A ReportsApiController binds http requests to an api service and writes the service results to the http response
type ReportsApiController struct {
	service         *ReportsApiService
	insightsService *insights.InsightsApiService
	authenticator   auth.Authenticator
}

// NewReportsApiController creates a default api controller
func NewReportsApiController(s *ReportsApiService, is *insights.InsightsApiService, a auth.Authenticator) rpApi.Router {
	return &ReportsApiController{service: s, insightsService: is, authenticator: a}
}

// Routes returns all of the api route for the ReportsApiController
func (c *ReportsApiController) Routes() rpApi.Routes {
	return rpApi.Routes{
		{
			Name:        "GetReportsId",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{id}",
			HandlerFunc: c.GetReportById,
		},
		{
			Name:        "GetTaggedReportsId",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{id}/taggedhtml",
			HandlerFunc: c.GetTaggedReportById,
		},
		{
			Name:        "GetFollowUpByReportId",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{id}/insights/followup",
			HandlerFunc: c.GetFollowUpByReportId,
		},
		{
			Name:        "GetFollowUpsByReportIds",
			Method:      strings.ToUpper("POST"),
			Pattern:     "/insights/followups",
			HandlerFunc: c.GetFollowUpsByReportIds,
		},
		{
			Name:        "GetQuestionsByReportId",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{id}/insights/questions",
			HandlerFunc: c.GetQuestionsByReportId,
		},
		{
			Name:        "GetOrganVizByReportId",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{id}/insights/organviz",
			HandlerFunc: c.GetOrganvizByReportId,
		},
		{
			Name:        "GetExplanationByReportId",
			Method:      strings.ToUpper("Get"),
			Pattern:     "/{id}/insights/explanation",
			HandlerFunc: c.GetExplanationByReportId,
		},
	}
}

func (c *ReportsApiController) GetPathPrefix() string {
	return "/v1/reports"
}

func (c *ReportsApiController) GetMiddleware() [](func(http.Handler) http.Handler) {
	return [](func(http.Handler) http.Handler){c.authenticator.ValidateReadRole}
}

// GetReportsId - GET report
func (c *ReportsApiController) GetReportById(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	id := params["id"]
	accept := r.Header.Get("Accept")
	if accept == "" {
		accept = "image/png"
	}
	isValid := false
	for _, a := range validAccepts {
		if a == accept {
			isValid = true
		}
	}
	if !isValid {
		logutils.CtxLogger(r.Context()).WithFields(logrus.Fields{
			"report_id": id,
			"accept":    accept,
		}).Error("invalid accept type")
		http.Error(w, "Bad request", http.StatusBadRequest)
		return
	}

	bytes, err := c.service.GetReportById(r.Context(), id, accept)
	if err != nil {
		panic(err)
	}

	// If no error, write the body and the result code, and set content type
	_, err = w.Write(bytes)
	if err != nil {
		panic(err)
	}
	w.Header().Set("Content-Type", accept)
}

// GetReportsId - GET report
func (c *ReportsApiController) GetTaggedReportById(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	id := params["id"]

	queryParams := r.URL.Query()
	// default subdict to ""
	subdict := queryParams.Get("subdictionary")

	res, err := c.service.GetTaggedReportById(r.Context(), id, subdict)
	if err != nil {
		panic(err)
	}

	rpApi.EncodeTaggedHTMLJSONResponse(res, nil, w)
}

func (c *ReportsApiController) GetFollowUpByReportId(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	reportId := params["id"]

	lg := logutils.CtxLogger(r.Context())
	lg = lg.WithField("report_id", reportId)

	resp, err := c.insightsService.FetchFollowup(r.Context(), reportId)
	if err != nil {
		lg.WithError(err).Error("Failed fetching followups")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	err = rpApi.EncodeJSONResponse(resp, nil, w)
	if err != nil {
		lg.WithError(err).Error("encoding json response failed")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}

func (c *ReportsApiController) GetFollowUpsByReportIds(w http.ResponseWriter, r *http.Request) {
	lg := logutils.CtxLogger(r.Context())

	// grab report ids from body
	var reportIds []string
	err := json.NewDecoder(r.Body).Decode(&reportIds)
	defer func() {
		err := r.Body.Close()
		if err != nil {
			lg.WithError(err).Warn("failed closing body")
		}
	}()
	if err != nil {
		lg.WithError(err).Error("failed parsing body as json")
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}
	lg = lg.WithField("report_ids", reportIds)

	// send request to report-insights
	resp, err := c.insightsService.InsightsSvc.GetFollowups(r.Context(), reportIds)
	if err != nil {
		lg.WithError(err).Error("failed asking report-insights for followups")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// transform response and send it back
	transformedResponse := make(map[string]insightssvc.FollowupResponse)
	for _, reportResponse := range resp {
		transformedResponse[reportResponse.ReportId] = reportResponse.Followups
	}
	err = rpApi.EncodeJSONResponse(transformedResponse, nil, w)
	if err != nil {
		lg.WithError(err).Error("encoding json response failed")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}

func (c *ReportsApiController) GetQuestionsByReportId(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	reportId := params["id"]

	lg := logutils.CtxLogger(r.Context())
	lg = lg.WithField("report_id", reportId)

	resp, err := c.insightsService.FetchQuestions(r.Context(), reportId)
	if err != nil {
		lg.WithError(err).Error("Failed fetching questions")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	err = rpApi.EncodeJSONResponse(resp, nil, w)
	if err != nil {
		lg.WithError(err).Error("encoding json response failed")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}

func (c *ReportsApiController) GetOrganvizByReportId(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	reportId := params["id"]

	model := r.URL.Query().Get("model")

	lg := logutils.CtxLogger(r.Context()).
		WithField("report_id", reportId).
		WithField("model", model)

	resp, err := c.insightsService.FetchOrganviz(r.Context(), reportId, model)
	if err != nil {
		lg.WithError(err).Error("Failed fetching organviz")

		if strings.Contains(err.Error(), "invalid model provided") {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}

		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	err = rpApi.EncodeJSONResponse(resp, nil, w)
	if err != nil {
		lg.WithError(err).Error("encoding json response failed")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}

func (c *ReportsApiController) GetExplanationByReportId(w http.ResponseWriter, r *http.Request) {
	params := mux.Vars(r)
	reportId := params["id"]

	lg := logutils.CtxLogger(r.Context())
	lg = lg.WithField("report_id", reportId)

	resp, err := c.insightsService.FetchExplanation(r.Context(), reportId)
	if err != nil {
		lg.WithError(err).Error("Failed fetching explanation")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	err = rpApi.EncodeJSONResponse(resp, nil, w)
	if err != nil {
		lg.WithError(err).Error("encoding json response failed")
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
	}
}
