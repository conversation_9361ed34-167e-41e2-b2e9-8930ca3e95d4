/*
 * reportprocessor
 *
 * API for PocketHealth report processing
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package reports

import (
	"context"

	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/reportprocessor/pkg/blob"
	"gitlab.com/pockethealth/reportprocessor/pkg/convert"
	"gitlab.com/pockethealth/reportprocessor/pkg/definitions"
	"gitlab.com/pockethealth/reportprocessor/pkg/rpApi"
	"gitlab.com/pockethealth/reportprocessor/pkg/tagger"
)

var validAccepts = []string{"text/html", "image/png", "application/pdf"}

// ReportsApiService is a service that implents the logic for the ReportsApiServicer
// This service should implement the business logic for every endpoint for the ReportsApi API.
// Include any external packages or services that will be required by this service.
type ReportsApiService struct {
	blobClient blob.Client
	defTagger  *tagger.Tagger
	defStore   definitions.DefStore
}

// NewReportsApiService creates a default api service
func NewReportsApiService(
	bc blob.Client,
	t *tagger.Tagger,
	ds definitions.DefStore,
) *ReportsApiService {
	return &ReportsApiService{blobClient: bc, defTagger: t, defStore: ds}
}

// GetReportsId - GET report
func (s *ReportsApiService) GetReportById(
	ctx context.Context,
	id string,
	accept string,
) ([]byte, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"report_id": id,
		"accept":    accept,
	})

	file, size, err := s.blobClient.Download(ctx, id)
	if err != nil {
		lg.WithError(err).Error("error getting report file from blob store")
		return nil, err //not found or db problem
	}
	lg = lg.WithField("file_size_kb", size/1000)
	path := file.Name()
	err = file.Close()
	if err != nil {
		lg.WithError(err).Error("error closing file")
	}
	// convert dicom file to pdf, html or png
	// pngs are written to disk.
	convBytes, _, err := convert.ConvertReport(accept, lg, path)
	if err != nil || convBytes == nil {
		lg.WithError(err).Error("error converting dicom file")
		return nil, err
	}
	return convBytes, nil

}

// GetReportsId - GET report
func (s *ReportsApiService) GetTaggedReportById(
	ctx context.Context,
	id string,
	subdict string,
) (rpApi.TaggedHTML, error) {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"report_id": id,
	})

	file, size, err := s.blobClient.Download(ctx, id)
	if err != nil {
		lg.WithError(err).Error("error getting report file from blob store")
		return rpApi.TaggedHTML{}, err //not found or db problem
	}
	path := file.Name()
	lg = lg.WithField("file_size_kb", size/1000)
	err = file.Close()
	if err != nil {
		lg.WithError(err).Error("error closing file")
	}
	// convert dicom file to pdf, html or png
	// pngs are written to disk.
	convBytes, format, err := convert.ConvertReport("text/html", lg, path)
	if err != nil || convBytes == nil {
		lg.WithError(err).Error("error converting dicom file")
		return rpApi.TaggedHTML{}, err
	}

	taggedBytes, terms, err := s.defTagger.TagFns[format](ctx, lg, convBytes)
	if err != nil {
		lg.WithError(err).Error("error tagging html, returning untagged html")
		return rpApi.TaggedHTML{Report: string(convBytes), Defs: make(map[string]string)}, nil
	}

	res := rpApi.TaggedHTML{Report: string(taggedBytes)}

	subdictId := s.defStore.GetSubdictionaryId(ctx, subdict)

	// TODO: remove
	// this is temporary hot fix for broken MCN for some users
	organs, err := s.defStore.GetOrganvizTerms(ctx, "all")
	if err != nil {
		lg.WithError(err).Error("error getting organviz terms")
		// dont return error here
	} else {
		terms = append(terms, organs...)
	}

	res.Defs, res.OrganVizTerms, err = s.defStore.GetDefinitions(ctx, terms, subdictId)
	if err != nil {
		lg.WithError(err).Error("error getting definitions, returning untagged html")
		return rpApi.TaggedHTML{Report: string(convBytes), Defs: make(map[string]string)}, nil
	}

	// add image_link and citation_id
	if len(terms) > 0 {
		res.Images, err = s.defStore.GetImages(ctx, terms, subdictId)
		if err != nil {
			lg.WithError(err).Error("error getting images, returning no image info")
			res.Images = make(map[string]definitions.ImageInfo)
			return res, nil
		}
	}

	return res, nil
}
