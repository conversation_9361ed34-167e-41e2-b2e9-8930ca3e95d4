package insights

import (
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/k3a/html2text"
	"github.com/sirupsen/logrus"
	"github.com/suyashkumar/dicom"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/reportprocessor/pkg/blob"
	"gitlab.com/pockethealth/reportprocessor/pkg/convert"
	"gitlab.com/pockethealth/reportprocessor/pkg/definitions"
	"gitlab.com/pockethealth/reportprocessor/pkg/dicomutils"
	"gitlab.com/pockethealth/reportprocessor/pkg/insights/insightssvc"
)

type InsightsJobDeps struct {
	ObjsBlob     blob.Client
	InsightsBlob blob.Client
	InsightsSvc  *insightssvc.InsightsClient
	DefStore     definitions.DefStore
}

func PrepareInsights(ctx context.Context, reportId string, deps InsightsJobDeps) {
	lg := logutils.CtxLogger(ctx).WithField("report_id", reportId)
	start := time.Now()
	// 1. check if raw text blob exists and if not, create it at `destinationBlobName`
	destinationBlobName := reportId
	err := PrepareRawText(ctx, deps.ObjsBlob, deps.InsightsBlob, reportId, destinationBlobName)
	if err != nil {
		lg.Error("no raw text available, skipping insights")
		return
	}

	// 2. call report insights to generate questions (so far just doing this async, don't need to do anything with the resp)
	_, err = deps.InsightsSvc.GetQuestions(ctx, reportId)
	if err != nil {
		lg.WithError(err).Error("error getting insights questions")
	}

	// 3. call report insights to generate followups
	_, err = deps.InsightsSvc.GetFollowup(ctx, reportId)
	if err != nil {
		lg.WithError(err).Error("error getting insights followup")
	}

	taken := time.Since(start)
	lg.WithFields(logrus.Fields{
		"duration_ms": taken.Milliseconds(),
	}).Info("processed report insights")
}

func PrepareRawText(
	ctx context.Context,
	objsBlobclient blob.Client,
	insightsBlobClient blob.Client,
	reportId string,
	destinationBlobName string,
) error {
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"report_id":             reportId,
		"destination_blob_name": destinationBlobName,
	})

	rawReportExists := insightsBlobClient.CanAccess(ctx, destinationBlobName)
	if rawReportExists {
		return nil
	}

	// extract pdf from blob
	rawTxtReader, err := downloadReportAndConvertToText(
		ctx,
		objsBlobclient,
		reportId,
	)
	if err != nil {
		lg.WithError(err).Error("failed to download pdf and extract text")
		return err
	}

	// write raw text to blob
	if rawTxtReader != nil {
		_, err = insightsBlobClient.Upload(ctx, destinationBlobName, rawTxtReader)
		if err != nil {
			lg.WithError(err).Error("unable to upload report raw text to storage")
			return err
		}
	}
	return nil
}

// returns the path to txt file on disk
func downloadReportAndConvertToText(
	ctx context.Context,
	blobClient blob.Client,
	reportId string,
) (io.Reader, error) {
	lg := logutils.CtxLogger(ctx).WithField("reportId", reportId)

	reportFile, _, err := blobClient.Download(ctx, reportId)
	if err != nil {
		return nil, fmt.Errorf("failed downloading file from blob storage: %w", err)
	}
	// need to seek to beginning of the downloaded report file before putting it into dicom.Parse()
	if _, err := reportFile.Seek(0, io.SeekStart); err != nil {
		lg.WithError(err).Errorf("error seeking to beginning of download res: %s", err)
		return nil, fmt.Errorf("error seeking to beginning of download res: %s", err)
	}

	txtPath := reportFile.Name() + ".txt"

	info, err := reportFile.Stat()
	if err != nil {
		lg.WithError(err).Error("could not get filesize to determine report format")
		return nil, fmt.Errorf("unable to read file size: %w", err)
	}

	// check what type of report it is:
	ds, err := dicom.Parse(reportFile, info.Size(), nil)
	if err != nil {
		lg.WithError(err).Error("could not parse dicom")
		return nil, fmt.Errorf("unable to parse dicom file: %w", err)
	}

	format := dicomutils.GetReportFormat(ds)

	if format == dicomutils.EncapPDF {
		defer func() {
			if err := os.Remove(txtPath); err != nil {
				lg.WithError(err).Error("error removing txt file")
			}
		}()
		err = pdftotext(reportFile.Name())
		if err != nil {
			_, errPath := exec.LookPath(txtPath)
			if errPath != nil {
				errMsg := "no text file as result of pdf conversion"
				lg.WithFields(logrus.Fields{
					"pdf_to_text_err": err,
					"lookup_err":      errPath,
				}).Error(errMsg)
				return nil, errors.New(errMsg)
			} else {
				lg.WithError(err).Error("PDF Conversion returned error, but txt got created")
			}
		}

		// return raw text reader
		if txtPath != "" {
			rawTxt, err := os.Open(txtPath) // #nosec G304
			if err != nil {
				lg.WithError(err).Error("failed to read file from path")
				return nil, err
			}
			return rawTxt, nil
		}
	} else if format == dicomutils.CareStreamSR || format == dicomutils.KonicaSR || format == dicomutils.RegSR {
		htmlBytes, err := convert.HtmlFromDicomSR(lg, reportFile.Name(), format)
		if err != nil {
			lg.WithError(err).Error("could not parse out html (or text) from SR report")
			return nil, nil
		}
		rawtxt := html2text.HTML2TextWithOptions(string(htmlBytes), html2text.WithUnixLineBreaks())
		return strings.NewReader(rawtxt), nil
	}

	return nil, errors.New("do not know how to convert this report to text")
}

func pdftotext(pdfPath string) error {
	cmd := exec.Command("pdftotext", pdfPath)
	return cmd.Run()
}
