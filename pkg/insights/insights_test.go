//go:build integration
// +build integration

package insights

import (
	"context"
	"io"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.com/pockethealth/reportprocessor/pkg/blob"
	"gitlab.com/pockethealth/reportprocessor/pkg/testutils"
)

func TestPrepareRawText(t *testing.T) {
	testutils.WDtoProjDir(t)
	t.Run("raw text already exists", func(t *testing.T) {
		objStore := blob.MockBlobStore{}
		insightsStore := blob.MockBlobStore{CanAccessVal: true}
		repId := "alreadyprocessed"

		err := PrepareRawText(context.TODO(), &objStore, &insightsStore, repId, repId)
		assert.Nil(t, err, "expected no error if raw text exists")

		assert.Equal(t, 0, len(objStore.DownloadCalledWith), "expected to skip download if text exists")
		assert.Equal(t, 0, len(insightsStore.UploadCalledWith), "expected to skip upload if text exists")
	})
	t.Run("error extracting raw text", func(t *testing.T) {
		//pass in something that's not a pdf or SR
		f, err := os.Open("apptest/sampledicom/nonEncapsulated.dcm")
		if err != nil {
			t.Fatalf("could not open file for testing: %v", err)
		}
		defer f.Close()

		objStore := blob.MockBlobStore{File: f}
		insightsStore := blob.MockBlobStore{CanAccessVal: false}

		repId := "notapdf"
		err = PrepareRawText(context.TODO(), &objStore, &insightsStore, repId, repId)
		assert.NotNil(t, err, "expected error when extraction fails")

		assert.Equal(t, 1, len(objStore.DownloadCalledWith), "expected report to be downloaded")
		assert.Equal(t, repId, objStore.DownloadCalledWith[0], "expected report being processed to be downloaded")
		assert.Equal(t, 0, len(insightsStore.UploadCalledWith), "expected to upload nothing if extraction fails")
	})
	t.Run("successfully get/upload raw text for encap pdf", func(t *testing.T) {
		// pass in something that's a pdf
		f, err := os.Open("apptest/sampledicom/encapsulatedPDF")
		if err != nil {
			t.Fatalf("could not open file for testing: %v", err)
		}
		defer f.Close()

		objStore := blob.MockBlobStore{File: f}
		insightsStore := blob.MockBlobStore{CanAccessVal: false}

		repId := "apdf"
		err = PrepareRawText(context.TODO(), &objStore, &insightsStore, repId, repId)
		assert.Nil(t, err, "expected no error")

		assert.Equal(t, 1, len(objStore.DownloadCalledWith), "expected report to be downloaded")
		assert.Equal(t, repId, objStore.DownloadCalledWith[0], "expected report being processed to be downloaded")

		assert.Equal(t, 1, len(insightsStore.UploadCalledWith), "expected one upload for successful extraction")
		assert.Equal(t, repId, insightsStore.UploadCalledWith[0], "expected one upload for successful extraction")

		expectedText := "Hello,\nThis is a test report\nLove,\nPH\n\n\f"
		assert.Equal(t, expectedText, string(insightsStore.UploadedContent), "unexpected text extraction")
	})
	t.Run("successfully get/upload raw text for carestream sr", func(t *testing.T) {
		// pass in something that's a pdf
		f, err := os.Open("apptest/sampledicom/careStreamSR")
		if err != nil {
			t.Fatalf("could not open file for testing: %v", err)
		}
		defer f.Close()

		objStore := blob.MockBlobStore{File: f}
		insightsStore := blob.MockBlobStore{CanAccessVal: false}

		repId := "apdf"
		err = PrepareRawText(context.TODO(), &objStore, &insightsStore, repId, repId)
		assert.Nil(t, err, "expected no error")

		assert.Equal(t, 1, len(objStore.DownloadCalledWith), "expected report to be downloaded")
		assert.Equal(t, repId, objStore.DownloadCalledWith[0], "expected report being processed to be downloaded")

		assert.Equal(t, 1, len(insightsStore.UploadCalledWith), "expected one upload for successful extraction")
		assert.Equal(t, repId, insightsStore.UploadCalledWith[0], "expected one upload for successful extraction")

		f, err = os.Open("apptest/sampledicom/careStreamSRexpected.txt")
		if err != nil {
			t.Fatalf("could not open expected text file: %v", err)
		}
		expectedTextBytes, err := io.ReadAll(f)
		if err != nil {
			t.Fatalf("could not read expected text file: %v", err)
		}
		assert.Equal(t, string(expectedTextBytes), string(insightsStore.UploadedContent), "unexpected text extraction")
	})
	t.Run("successfully get/upload raw text for non-carestream sr", func(t *testing.T) {
		// pass in something that's a pdf
		f, err := os.Open("apptest/sampledicom/nonCareStreamSR")
		if err != nil {
			t.Fatalf("could not open file for testing: %v", err)
		}
		defer f.Close()

		objStore := blob.MockBlobStore{File: f}
		insightsStore := blob.MockBlobStore{CanAccessVal: false}

		repId := "apdf"
		err = PrepareRawText(context.TODO(), &objStore, &insightsStore, repId, repId)
		assert.Nil(t, err, "expected no error")

		assert.Equal(t, 1, len(objStore.DownloadCalledWith), "expected report to be downloaded")
		assert.Equal(t, repId, objStore.DownloadCalledWith[0], "expected report being processed to be downloaded")

		assert.Equal(t, 1, len(insightsStore.UploadCalledWith), "expected one upload for successful extraction")
		assert.Equal(t, repId, insightsStore.UploadCalledWith[0], "expected one upload for successful extraction")

		f, err = os.Open("apptest/sampledicom/nonCareStreamSRexpected.txt")
		if err != nil {
			t.Fatalf("could not open expected text file: %v", err)
		}
		expectedTextBytes, err := io.ReadAll(f)
		if err != nil {
			t.Fatalf("could not read expected text file: %v", err)
		}
		assert.Equal(t, string(expectedTextBytes), string(insightsStore.UploadedContent), "unexpected text extraction")
	})
}
