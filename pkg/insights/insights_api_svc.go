package insights

import (
	"context"
	"fmt"

	"gitlab.com/pockethealth/reportprocessor/pkg/insights/insightssvc"
)

type InsightsApiService struct {
	InsightsJobDeps
}

func (s *InsightsApiService) FetchFollowup(ctx context.Context, reportId string) (*insightssvc.FollowupResponse, error) {
	// check if raw text blob exists and if not, create it
	err := PrepareRawText(ctx, s.ObjsBlob, s.InsightsBlob, reportId, reportId)
	if err != nil {
		return nil, err
	}

	return s.InsightsSvc.GetFollowup(ctx, reportId)
}

func (s *InsightsApiService) FetchQuestions(ctx context.Context, reportId string) (*insightssvc.QuestionResponse, error) {
	// check if raw text blob exists and if not, create it
	err := PrepareRawText(ctx, s.Obj<PERSON>Blob, s.InsightsBlob, reportId, reportId)
	if err != nil {
		return nil, err
	}
	return s.InsightsSvc.GetQuestions(ctx, reportId)
}

func (s *InsightsApiService) FetchOrganviz(ctx context.Context, reportId string, model string) (*insightssvc.OrganvizResponse, error) {
	// Validate model if provided
	if model != "" && !insightssvc.IsValidModel(model) {
		return nil, fmt.Errorf("invalid model: %s", model)
	}

	organs, err := s.DefStore.GetOrganvizTerms(ctx, model)
	if err != nil {
		return nil, fmt.Errorf("failed getting organviz terms: %w", err)
	}

	return s.InsightsSvc.GetOrganviz(ctx, reportId, organs, model)
}

func (s *InsightsApiService) FetchExplanation(ctx context.Context, reportId string) (*insightssvc.ExplanationResponse, error) {
	reportBlobPath := "report-explanation/inputs/" + reportId
	// check if raw text blob exists and if not, create it
	err := PrepareRawText(ctx, s.ObjsBlob, s.InsightsBlob, reportId, reportBlobPath)
	if err != nil {
		return nil, err
	}
	return s.InsightsSvc.GetExplanation(ctx, reportId)
}
