package insightssvc

type FollowUp struct {
	Context       string  `json:"context"`
	MinTimeFrame  float32 `json:"min_time_frame"`
	MaxTimeFrame  float32 `json:"max_time_frame"`
	TimeFrameUnit string  `json:"time_frame_unit"`
	Category      string  `json:"category"`
}

type FollowupResponse struct {
	Exists      bool       `json:"exists"`
	Occurrences []FollowUp `json:"occurrences"`
}

type FollowupBatchResponse = []struct {
	ReportId  string           `json:"report_id"`
	Followups FollowupResponse `json:"followup"` // note singular here
}

type QuestionResponse struct {
	Questions []string `json:"questions"`
}

type OrganvizResponse struct {
	Organs []OrganVisualization `json:"organs"`
}

type OrganVisualization struct {
	ModelVersion string `json:"model_version"`
	Segmentation string `json:"segmentation"`
	BodyPart     string `json:"body_part"`
	Status       string `json:"status"`
}

type ExplanationResponse struct {
	Explanation string `json:"explanation"`
}
