package insightssvc

import (
	"net/http"
	"net/http/httptest"
)

// sets up a test server to pretend to be report insights service.
// you MUST run `.Close()` on the server after you are finished
func NewTestServer() *httptest.Server {

	// #nosec
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/v1/followup/notfound", "/v1/questions/notfound":
			w.<PERSON><PERSON><PERSON><PERSON>(http.StatusNotFound)
			_, err := w.Write([]byte(`{"message":"not found"}`))
			if err != nil {
				return
			}
			return
		case "/v1/followup/followup-exists":
			w.<PERSON><PERSON><PERSON><PERSON><PERSON>(http.StatusOK)
			_, err := w.Write([]byte(`{"exists":true, "occurrences": [{"context":"follow up exists"}]}`))
			if err != nil {
				return
			}
			return
		case "/v1/questions/questions-exist":
			w.<PERSON><PERSON><PERSON>(http.StatusOK)
			_, err := w.Write([]byte(`{"questions":["How is babby formed?", "wat?"]}`))
			if err != nil {
				return
			}
			return
		}
		w.<PERSON><PERSON><PERSON>ead<PERSON>(http.StatusBadRequest)
		_, err := w.Write([]byte(`""`))
		if err != nil {
			return
		}
	}))
	return server
}
