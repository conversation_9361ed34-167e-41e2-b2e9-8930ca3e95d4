package insightssvc

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/samber/lo"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type InsightsClient struct {
	url         string
	basicAuthPw string
	s2sClient   *httpclient.Client
}

type insightType string

const (
	typeFollowup    insightType = "followup"
	typeQuestions   insightType = "questions"
	typeExplanation insightType = "explanation"
)

func (i insightType) valid() bool {
	switch i {
	case typeFollowup, typeQuestions, typeExplanation:
		return true
	}
	return false
}

func IsValidModel(model string) bool {
	validModels := []string{"ct_abd", "xray_chest", "mri_abd"}
	return lo.Contains(validModels, model)
}

// S2SNewClient instantiates the InsightsClient
func S2SNewClient(url string, basicAuthPw string, s2sClient *httpclient.Client) *InsightsClient {
	return &InsightsClient{
		url:         url,
		basicAuthPw: basicAuthPw,
		s2sClient:   s2sClient,
	}
}

// GetFollowup fetches "followup" data for a single report
func (s *InsightsClient) GetFollowup(ctx context.Context, reportId string) (*FollowupResponse, error) {
	resp, err := s.getInsightResp(ctx, reportId, typeFollowup)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			// If context was canceled, we return an empty struct and no error
			return &FollowupResponse{}, nil
		}
		return nil, err
	}

	var followup FollowupResponse
	if err = s.unpackObject(ctx, resp, &followup); err != nil {
		return nil, err
	}

	return &followup, nil
}

// GetFollowups fetches "followup" data for multiple reportIds (bulk endpoint)
func (s *InsightsClient) GetFollowups(ctx context.Context, reportIds []string) (FollowupBatchResponse, error) {
	requestBody, err := json.Marshal(map[string]any{"report_ids": reportIds})
	if err != nil {
		return FollowupBatchResponse{}, fmt.Errorf("failed converting request body into json %w", err)
	}

	targetUrl := fmt.Sprintf("%s/v1/followup/bulk", s.url)
	res, status, err := s.s2sClient.SendRequest(ctx, httpclient.RequestParameters{
		HTTPMethod:    http.MethodPost,
		TargetURL:     targetUrl,
		AuthScheme:    httpclient.Bearer,
		ReqBody:       requestBody,
		ExpectedCodes: []int{http.StatusOK, http.StatusBadRequest, http.StatusNotFound, http.StatusUnprocessableEntity},
	})
	if status == http.StatusNotFound {
		return nil, errors.New("not found")
	} else if status == http.StatusUnprocessableEntity {
		return nil, errors.New("invalid request body")
	} else if err != nil {
		return nil, fmt.Errorf("failed sending http request to reportinsights through s2sclient: %w", err)
	}

	var followups FollowupBatchResponse
	err = s.unpackObject(ctx, res, &followups)
	if err != nil {
		return nil, fmt.Errorf("failed unpacking response body: %w", err)
	}

	return followups, nil
}

func (s *InsightsClient) GetQuestions(ctx context.Context, reportId string) (*QuestionResponse, error) {
	resp, err := s.getInsightResp(ctx, reportId, typeQuestions)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			return &QuestionResponse{}, nil
		}
		return nil, err
	}
	var questionResp QuestionResponse
	err = s.unpackObject(ctx, resp, &questionResp)

	if err != nil {
		return nil, err
	}
	return &questionResp, nil
}

func (s *InsightsClient) GetOrganviz(ctx context.Context, reportId string, organs []string, model string) (*OrganvizResponse, error) {
	requestBody, err := json.Marshal(map[string]any{"body_parts": organs})
	if err != nil {
		return nil, fmt.Errorf("failed converting request body into json: %w", err)
	}

	var queryParam string
	if model != "" {
		if !IsValidModel(model) {
			return nil, fmt.Errorf("invalid model provided: %q (must be in 'ct_abd', 'xray_chest', 'mri_abd')", model)
		}
		queryParam = "?model=" + model
	}
	targetURL := fmt.Sprintf("%s/v1/organviz/%s%s", s.url, reportId, queryParam)

	res, status, err := s.s2sClient.SendRequest(ctx, httpclient.RequestParameters{
		HTTPMethod:    http.MethodPost,
		TargetURL:     targetURL,
		AuthScheme:    httpclient.Bearer,
		ReqBody:       requestBody,
		ExpectedCodes: []int{http.StatusOK, http.StatusBadRequest},
	})
	if !is2xx(status) {
		return nil, fmt.Errorf("got non ok status %v", status)
	} else if err != nil {
		return nil, fmt.Errorf("failed sending http request to reportinsights through s2sclient: %w", err)
	}

	var parsedResponse OrganvizResponse
	err = s.unpackObject(ctx, res, &parsedResponse)
	if err != nil {
		return nil, err
	}
	return &parsedResponse, nil
}

func (s *InsightsClient) GetExplanation(ctx context.Context, reportId string) (*ExplanationResponse, error) {
	resp, err := s.getInsightResp(ctx, reportId, typeExplanation)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			return &ExplanationResponse{}, nil
		}
		return nil, err
	}
	var parsedResp ExplanationResponse
	err = s.unpackObject(ctx, resp, &parsedResp)

	if err != nil {
		return nil, err
	}
	return &parsedResp, nil
}

func is2xx(num int) bool {
	return num >= 200 && num < 300
}

func (s *InsightsClient) getInsightResp(ctx context.Context, reportId string, insightType insightType) (io.ReadCloser, error) {
	if !insightType.valid() {
		return nil, fmt.Errorf("insight type %s not valid", insightType)
	}

	insightEndpoint := fmt.Sprintf("%s/v1/%s/%s", s.url, insightType, reportId)
	lg := logutils.CtxLogger(ctx).WithFields(logrus.Fields{
		"report_id":        reportId,
		"insight_type":     insightType,
		"insight_endpoint": insightEndpoint,
	})

	res, status, err := s.s2sClient.SendRequest(ctx, httpclient.RequestParameters{
		HTTPMethod:    http.MethodGet,
		TargetURL:     insightEndpoint,
		AuthScheme:    httpclient.Bearer,
		ExpectedCodes: []int{http.StatusOK, http.StatusBadRequest},
	})
	if status != http.StatusOK {
		lg.WithError(err).Errorf("Non OK status (%d) for request URL: %s", status, s.url)
		if status == http.StatusNotFound {
			return nil, errors.New("not found")
		}
		return nil, errors.New("got non ok status from reportinsight")
	} else if err != nil {
		return nil, fmt.Errorf("failed sending http request to reportinsights through s2sclient: %w", err)
	}

	return res, nil

}

func (s *InsightsClient) unpackObject(ctx context.Context, reader io.ReadCloser, obj any) error {
	lg := logutils.CtxLogger(ctx)

	gotBody, err := io.ReadAll(reader)
	if err != nil {
		return err
	}

	defer func() {
		err := reader.Close()
		if err != nil {
			lg.WithError(err).Error("error closing reader")
		}
	}()

	err = json.Unmarshal(gotBody, &obj)
	if err != nil {
		lg.WithError(err).Errorf("error unmarshaling response body: %s", string(gotBody))
		return err
	}

	return nil
}

func (s *InsightsClient) setupBasicAuth() string {
	cred := fmt.Sprintf("%s:%s", "rp", s.basicAuthPw)
	return base64.StdEncoding.EncodeToString([]byte(cred))
}
