package insightssvc

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"gitlab.com/pockethealth/phutils/v10/pkg/auth/service"
	"gitlab.com/pockethealth/reportprocessor/pkg/testutils"
	"golang.org/x/oauth2"

	"github.com/google/go-cmp/cmp"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
)

func TestReportInsights(t *testing.T) {
	server := NewTestServer()
	defer server.Close()

	ctx := context.Background()

	tokenSrc := testutils.NewMockTokenSource()
	baseClient := oauth2.NewClient(ctx, tokenSrc)
	client := S2SNewClient(server.URL, "", httpclient.NewHTTPClient(baseClient, nil))

	t.Run("not found", func(t *testing.T) {
		_, err := client.GetFollowup(ctx, "notfound")
		_, err2 := client.GetQuestions(ctx, "notfound")

		cases := []struct {
			expected error
		}{
			{expected: err},
			{expected: err2},
		}
		for _, testCase := range cases {
			if testCase.expected == nil {
				t.Fatal("expected an error but got none")
			}

			if testCase.expected.Error() != "not found" {
				t.Fatalf("expected report not found but got %s", testCase.expected.Error())
			}
		}
	})

	t.Run("followup success", func(t *testing.T) {
		followup, err := client.GetFollowup(ctx, "followup-exists")
		if err != nil {
			t.Fatalf("expected no error but got: %v", err)
		}
		if followup == nil {
			t.Fatal("expected followup not to be nil")
		}
		expected := FollowupResponse{
			Exists:      true,
			Occurrences: []FollowUp{{Context: "follow up exists"}},
		}
		if !cmp.Equal(followup, &expected) {
			t.Fatalf("expected %v but got %v", expected, followup)
		}
	})

	t.Run("questions success", func(t *testing.T) {
		questions, err := client.GetQuestions(ctx, "questions-exist")
		if err != nil {
			t.Fatalf("expected no error but got: %v", err)
		}
		if questions == nil {
			t.Fatal("expected questions not to be nil")
		}
		expected := QuestionResponse{
			Questions: []string{
				"How is babby formed?", "wat?",
			},
		}
		if !cmp.Equal(questions, &expected) {
			t.Fatalf("expected %v but got %v", expected, questions)
		}
	})
}

func TestRIS2SSucceed(t *testing.T) {
	audience := "reportinsights"
	authUrl := "https://gatewayauth.global.qa.pocket.health"
	resourceUrl := "https://ri.cactrl.qa.pocket.health"
	reportID := "-1zvY2i_264SDjDJwtKIQt3xAImw0mnkOzyJL3aqoIc="
	reportID2 := "-2_PF-i60aIULnX4BWnhdkGMdvQ9m3l_TNXi_yEUSho="

	ctx := context.Background()

	curDir, err := os.Getwd()
	if err != nil {
		t.Fatal("error getting current directory:", err)
	}
	tokenPath := filepath.Join(testutils.FindProjectRoot(curDir, "reportprocessor"), "tmp", "token.txt")
	tokenSrc := service.NewTokenSource(tokenPath, authUrl, audience, ctx)
	baseClient := oauth2.NewClient(ctx, oauth2.ReuseTokenSource(nil, tokenSrc))

	client := S2SNewClient(resourceUrl, "", httpclient.NewHTTPClient(baseClient, nil))

	t.Run("followup success", func(t *testing.T) {
		followup, err := client.GetFollowup(ctx, reportID)
		if err != nil {
			t.Fatalf("expected no error but got: %v", err)
		}
		if followup == nil {
			t.Fatal("expected followup not to be nil")
		}
	})

	t.Run("questions success", func(t *testing.T) {
		questions, err := client.GetQuestions(ctx, reportID)
		if err != nil {
			t.Fatalf("expected no error but got: %v", err)
		}
		if questions == nil {
			t.Fatal("expected questions not to be nil")
		}
	})

	t.Run("bulk followup (followups) success", func(t *testing.T) {
		followups, err := client.GetFollowups(ctx, []string{reportID, reportID2})
		if err != nil {
			t.Fatalf("expected no error but got: %v", err)
		}
		if followups == nil {
			t.Fatal("expected followups not to be nil")
		}
	})
}
