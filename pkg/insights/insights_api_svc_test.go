package insights_test

import (
	"context"
	"io"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/pockethealth/phutils/v10/pkg/httpclient"
	"gitlab.com/pockethealth/reportprocessor/pkg/blob"
	"gitlab.com/pockethealth/reportprocessor/pkg/definitions"
	"gitlab.com/pockethealth/reportprocessor/pkg/insights"
	"gitlab.com/pockethealth/reportprocessor/pkg/insights/insightssvc"
	"gitlab.com/pockethealth/reportprocessor/pkg/testutils"
	"golang.org/x/oauth2"
)

func TestFetchOrganviz_expectedOrgansSentToReportInsights(t *testing.T) {
	server := testutils.NewMockHttpServer(t, map[string]insightssvc.OrganvizResponse{
		"/v1/organviz/some-report-id": {Organs: []insightssvc.OrganVisualization{{}}},
	})
	defer server.Close()

	mockDefStore := &definitions.MockDefStore{}

	// set organviz terms
	mockDefStore.SetOrganvizTerms("liver", "spleen")

	tokenSrc := testutils.NewMockTokenSource()
	client := oauth2.NewClient(context.Background(), tokenSrc)

	sut := insights.InsightsApiService{
		InsightsJobDeps: insights.InsightsJobDeps{
			ObjsBlob:     nil,
			InsightsBlob: nil,
			InsightsSvc: insightssvc.S2SNewClient(
				server.URL,
				"",
				httpclient.NewHTTPClient(client, nil),
			),
			DefStore: mockDefStore,
		},
	}

	reportID := "some-report-id"

	t.Run("no model", func(t *testing.T) {
		server.Reset() // Clear previous calls

		_, err := sut.FetchOrganviz(context.Background(), reportID, "")
		require.NoError(t, err)

		// Assert request contains the expected organs and no model query
		endpointCalls := server.EndpointCalls("/v1/organviz/" + reportID)
		require.Equal(t, 1, len(endpointCalls))

		body, err := io.ReadAll(endpointCalls[0].Body)
		require.NoError(t, err)
		assert.JSONEq(t, `{"body_parts":["liver","spleen"]}`, string(body))
	})

	// Test with model="ct_abd"
	t.Run("model=ct_abd", func(t *testing.T) {
		server.Reset()

		_, err := sut.FetchOrganviz(context.Background(), reportID, "ct_abd")
		require.NoError(t, err)

		// Verify endpoint call with query parameter
		calls := server.EndpointCalls("/v1/organviz/" + reportID)
		require.Equal(t, 1, len(calls))

		// Check query parameter
		require.Equal(t, "ct_abd", calls[0].URL.Query().Get("model"))

		data, err := io.ReadAll(calls[0].Body)
		require.NoError(t, err)
		assert.JSONEq(t, `{"body_parts":["liver","spleen"]}`, string(data))
	})

	// Test with model="xray"
	t.Run("model=xray_chest", func(t *testing.T) {
		server.Reset() // Clear previous calls

		_, err := sut.FetchOrganviz(context.Background(), reportID, "xray_chest")
		require.NoError(t, err)

		calls := server.EndpointCalls("/v1/organviz/" + reportID)
		require.Equal(t, 1, len(calls))

		// Check query parameter
		require.Equal(t, "xray_chest", calls[0].URL.Query().Get("model"))

		data, err := io.ReadAll(calls[0].Body)
		require.NoError(t, err)
		assert.JSONEq(t, `{"body_parts":["liver","spleen"]}`, string(data))
	})

	t.Run("model=mri_abd", func(t *testing.T) {
		server.Reset() // Clear previous calls

		_, err := sut.FetchOrganviz(context.Background(), reportID, "mri_abd")
		require.NoError(t, err)

		calls := server.EndpointCalls("/v1/organviz/" + reportID)
		require.Equal(t, 1, len(calls))

		// Check query parameter
		require.Equal(t, "mri_abd", calls[0].URL.Query().Get("model"))

		data, err := io.ReadAll(calls[0].Body)
		require.NoError(t, err)
		assert.JSONEq(t, `{"body_parts":["liver","spleen"]}`, string(data))
	})

	// Test with an invalid model
	t.Run("invalid model", func(t *testing.T) {
		server.Reset()

		_, err := sut.FetchOrganviz(context.Background(), reportID, "random model")
		require.Error(t, err)

		// Ensure no calls were made to the server
		calls := server.EndpointCalls("/v1/organviz/" + reportID)
		require.Equal(t, 0, len(calls))
	})
}

func TestFetchExplanation(t *testing.T) {
	server := testutils.NewMockHttpServer(t, map[string]any{
		"/v1/explanation/some-report-id": map[string]any{"explanation": "some explanation"},
	})
	server.LogIncomingRequests = true
	defer server.Close()

	tokenSrc := testutils.NewMockTokenSource()
	client := oauth2.NewClient(context.Background(), tokenSrc)

	// set up a mock blob store returning a sample dicom file
	sampleDicomPath, err := filepath.Abs("apptest/sampledicom/encapsulatedPDF")
	require.NoError(t, err)
	dicomFile, err := os.Open(sampleDicomPath)
	require.NoError(t, err)
	mockBlobStore := &blob.MockBlobStore{
		CanAccessVal: false,
		File:         dicomFile,
	}

	sut := insights.InsightsApiService{
		InsightsJobDeps: insights.InsightsJobDeps{
			ObjsBlob:     mockBlobStore,
			InsightsBlob: mockBlobStore,
			InsightsSvc: insightssvc.S2SNewClient(
				server.URL,
				"", // basic auth pw
				httpclient.NewHTTPClient(client, nil),
			),
		},
	}

	// make request
	reportId := "some-report-id"
	_, err = sut.FetchExplanation(context.Background(), reportId)
	require.NoError(t, err)

	t.Run("report is uploaded to reportinsights", func(t *testing.T) {
		blobPath := "report-explanation/inputs/" + reportId
		assert.Equal(t, mockBlobStore.UploadCalledWith, []string{blobPath}, "expected blob to be uploaded to path: %s", blobPath)
	})

	t.Run("request is sent to reportinsights", func(t *testing.T) {
		// assert request contains the expected organs
		endpointCalls := server.EndpointCalls("/v1/explanation/" + reportId)
		require.Equal(t, 1, len(endpointCalls), "expected 1 call to reportinsights")

		b, err := io.ReadAll(endpointCalls[0].Body)
		require.NoError(t, err)
		assert.Empty(t, b, "expected empty request body")
	})
}
