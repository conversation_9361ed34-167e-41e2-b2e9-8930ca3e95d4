//go:build integration
// +build integration

package blob

import (
	"context"
	"io"
	"os"
	"strings"
	"testing"

	"github.com/segmentio/ksuid"
	"gitlab.com/pockethealth/reportprocessor/pkg/testutils"
)

func TestBlob(t *testing.T) {
	cfg := testutils.LoadTestConfigFromEnvVar(t)
	c, err := NewBlobStoreClient(cfg.AzStorageAccount, cfg.AzContainer)
	if err != nil {
		t.Fatalf("error setting up blob store client: %s", err)
	}
	ctx := context.Background()
	var id string

	t.Cleanup(func() {
		c.Delete(context.Background(), id)
	})

	t.Run("successfully uploads blob", func(t *testing.T) {
		var err error
		id = "test_id" + ksuid.New().String()
		t.Cleanup(func() {
			c.Delete(context.Background(), id)
		})
		_, err = c.Upload(ctx, id, strings.NewReader("test content"))
		if err != nil {
			t.Fatalf("error uploading blob for download set up: %s", err)
		}
	})

	t.Run("successfully downloads blob", func(t *testing.T) {
		defer os.RemoveAll("vault")
		err := os.Mkdir("vault", 0700)
		if err != nil {
			t.Fatalf("could not create vault folder: %v", err)
		}
		downloadRes, size, err := c.Download(ctx, "test_id")
		if err != nil {
			t.Fatalf("error downloading blob: %s", err)
		}

		if _, err := downloadRes.Seek(0, io.SeekStart); err != nil {
			t.Fatalf("error seeking to beginning of download res: %s", err)
		}
		if err := downloadRes.Sync(); err != nil {
			t.Fatalf("error syncing to beginning of file: %s", err)
		}

		bytesDownloaded := make([]byte, size)
		downloadRes.Read(bytesDownloaded)

		if s := string(bytesDownloaded); s != "test content" {
			t.Fatalf("expected: %s, got: %s", "test content", s)
		}
	})

	t.Run("check if blob exists (false)", func(t *testing.T) {
		if c.CanAccess(ctx, "definitelynotarealid"+ksuid.New().String()) {
			t.Fatalf("should not be able to access fake blob")
		}
	})

	t.Run("check if blob exists (true)", func(t *testing.T) {
		var err error
		id = "test_id" + ksuid.New().String()
		t.Cleanup(func() {
			c.Delete(context.Background(), id)
		})
		_, err = c.Upload(ctx, id, strings.NewReader("test content"))
		if err != nil {
			t.Fatalf("error uploading blob for CanAccess set up: %s", err)
		}
		if !c.CanAccess(ctx, id) {
			t.Fatalf("should be able to access blob")
		}
	})
}
