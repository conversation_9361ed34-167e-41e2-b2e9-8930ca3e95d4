package blob

import (
	"context"
	"fmt"
	"io"
	"os"
	"time"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/policy"
	_ "github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/blob"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/container"
	_ "github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/service"
	"github.com/sirupsen/logrus"
	"gitlab.com/pockethealth/phutils/v10/pkg/azstorageauth"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
)

type Client interface {
	Download(ctx context.Context, id string) (*os.File, int64, error)
	Upload(ctx context.Context, id string, file io.Reader) (string, error)
	Delete(ctx context.Context, id string) error
	CanAccess(ctx context.Context, id string) bool
}

type AzBlobClient struct {
	bsc *container.Client
}

func NewBlobStoreClient(
	accountName string, containerName string) (Client, error) {

	retryOptions := &policy.RetryOptions{
		MaxRetries:    2,
		TryTimeout:    time.Minute * 10,
		RetryDelay:    time.Second * 1,
		MaxRetryDelay: time.Second * 2,
	}
	ctx := context.Background()

	lg := logutils.CtxLogger(ctx)

	blobClient, err := azstorageauth.GetContainerClient(ctx, accountName, containerName, retryOptions)

	if err != nil {
		lg.WithError(err).Error("error setting up blob store container")
		return nil, err
	}
	return &AzBlobClient{bsc: blobClient}, nil
}

// Download downloads a blob data to a tmp file given the objectID
func (abc *AzBlobClient) Download(ctx context.Context, id string) (*os.File, int64, error) {
	lg := logutils.CtxLogger(ctx).WithField("id", id)

	objectBlobClient := abc.bsc.NewBlobClient(id)
	tmp, err := os.CreateTemp("vault", id+".")
	if err != nil {
		lg.WithError(err).Error("can't create temp file for object")
		return nil, 0, fmt.Errorf("cannot create temp file")
	}

	start := time.Now()
	_, err = objectBlobClient.DownloadFile(ctx, tmp, &blob.DownloadFileOptions{})
	if err != nil {
		lg.WithField("blob", objectBlobClient).Error("Download error")
		return nil, 0, err
	}

	dlTime := time.Since(start)
	stat, err := tmp.Stat()
	if err != nil {
		lg.WithError(err).Error("couldn't stat file")
		return nil, 0, err
	}
	lg.WithFields(logrus.Fields{
		"dl_time_ms": dlTime.Milliseconds(),
		"size_kb":    stat.Size() / 1000,
	}).Info("download stats")
	return tmp, stat.Size(), nil
}

// Upload uploads blob data from a multipart file given a unique UUID.
func (abc *AzBlobClient) Upload(ctx context.Context, id string, file io.Reader) (string, error) {
	lg := logutils.CtxLogger(ctx).WithField("id", id)
	objectBlobClient := abc.bsc.NewBlockBlobClient(id)
	_, err := objectBlobClient.UploadStream(ctx, file, nil)
	if err != nil {
		lg.WithError(err).Errorf("error uploading blob")
		return "", err
	}
	lg.WithFields(logrus.Fields{
		"url": abc.bsc.NewBlobClient(id).URL(),
	}).Info("upload stats")
	return abc.bsc.NewBlobClient(id).URL(), nil
}

// Delete deletes the blob given the id
func (abc *AzBlobClient) Delete(ctx context.Context, id string) error {
	lg := logutils.CtxLogger(ctx).WithField("id", id)

	objectBlobClient := abc.bsc.NewBlobClient(id)

	_, err := objectBlobClient.Delete(ctx, &blob.DeleteOptions{})
	if err != nil {
		lg.WithError(err).Errorf("error deleting blob")
		return err
	}

	return nil
}

// CanAccess returns true if the blob reader can be accessed
// a blob can be inaccessible if it doesn't exist or if there's an azure db problem
func (abc *AzBlobClient) CanAccess(ctx context.Context, id string) bool {
	objectBlobClient := abc.bsc.NewBlobClient(id)
	_, err := objectBlobClient.DownloadStream(ctx, &blob.DownloadStreamOptions{})
	return err == nil
}
