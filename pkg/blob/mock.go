package blob

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"sync"
	"testing"
)

// define mocks
type MockBlobStore struct {
	File               *os.File
	CanAccessVal       bool
	UploadCalledWith   []string
	DownloadCalledWith []string
	UploadedContent    string

	// some of our code requires downloaded files to be in the working directory
	// so, to clean up correctly in those cases we need the test handler to
	// trigger the clean up
	TestHandle *testing.T

	fileContents  *bytes.Reader
	copyFileMutex sync.Mutex
}

func (mb *MockBlobStore) Download(ctx context.Context, id string) (*os.File, int64, error) {
	mb.DownloadCalledWith = append(mb.DownloadCalledWith, id)

	// files can only be read from once, so to allow the file returned from
	// Download to be read multiple times we need to return a copy of it instead
	// of the original
	return mb.copyFile(mb.File)
}

func (mb *MockBlobStore) copyFile(original *os.File) (*os.File, int64, error) {
	// because the file system is touched here we need to make sure only one
	// goroutine is in here at any time
	mb.copyFileMutex.Lock()
	defer mb.copyFileMutex.Unlock()

	err := mb.cacheFileContents(original)
	if err != nil {
		return nil, 0, fmt.Errorf("failed preparing file for download: %w", err)
	}

	copy, err := mb.createTempFileWithContents(mb.fileContents)
	if err != nil {
		return nil, 0, fmt.Errorf("failed generating file to download: %w", err)
	}

	return copy, int64(mb.fileContents.Len()), nil
}

func (mb *MockBlobStore) cacheFileContents(file *os.File) error {
	if mb.fileContents == nil {
		fileContents, err := io.ReadAll(file)
		if err != nil {
			return fmt.Errorf("unable to read file %s: %w", file.Name(), err)
		}
		mb.fileContents = bytes.NewReader(fileContents)
	}
	return nil
}

func (mb *MockBlobStore) createTempFileWithContents(source io.ReadSeeker) (*os.File, error) {
	tempDir := "" // use OS's temp dir so the file will be removed at some point
	if mb.TestHandle != nil {
		// we have a test handler we can use to clean up, so put files in WD
		tempDir = "."
	}

	copy, err := os.CreateTemp(
		tempDir,
		"report-processor-mockblobstore-*",
	)
	if err != nil {
		return nil, fmt.Errorf("unable to create temporary file: %w", err)
	}
	if mb.TestHandle != nil {
		mb.TestHandle.Cleanup(func() {
			err := os.Remove(copy.Name())
			mb.TestHandle.Logf("Unable to remove temp file: %v", err)
		})
	}

	_, err = io.Copy(copy, source)
	if err != nil {
		return nil, fmt.Errorf("failed copying file data: %w", err)
	}

	// because we just wrote to `copy`, we need to reset its seeker to the start
	// or we won't be able to read the data just written
	_, err = copy.Seek(0, io.SeekStart)
	if err != nil {
		return nil, fmt.Errorf("failed resetting reader in temp file: %w", err)
	}

	// reset the source so it can be read again
	_, err = source.Seek(0, io.SeekStart)
	if err != nil {
		return nil, fmt.Errorf("unable to reset seeker: %w", err)
	}

	return copy, nil
}

func (mb *MockBlobStore) Upload(ctx context.Context, id string, file io.Reader) (string, error) {
	mb.UploadCalledWith = append(mb.UploadCalledWith, id)
	var buf []byte
	buf, _ = io.ReadAll(file)
	mb.UploadedContent = string(buf)
	return "", nil
}
func (mb *MockBlobStore) Delete(ctx context.Context, id string) error   { return nil }
func (mb *MockBlobStore) CanAccess(ctx context.Context, id string) bool { return mb.CanAccessVal }
