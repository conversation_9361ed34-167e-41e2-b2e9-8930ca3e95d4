/*
 * reportprocessor
 *
 * API for PocketHealth report processing
 *
 * API version: 1.0
 * Contact: <EMAIL>
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package rpApi

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gorilla/mux"
)

// A Route defines the parameters for an api endpoint
type Route struct {
	Name        string
	Method      string
	Pattern     string
	HandlerFunc http.HandlerFunc
}

// Routes are a collection of defined api endpoints
type Routes []Route

// Router defines the required methods for retrieving api routes
type Router interface {
	Routes() Routes
	GetPathPrefix() string
	GetMiddleware() []func(http.Handler) http.Handler
}

// NewRouter creates a new router for any number of api routers
// and adds each api router's associated middleware
func NewRouter(routers ...Router) *mux.Router {
	mRouter := mux.NewRouter().StrictSlash(false)

	for _, api := range routers {
		//create a subrouter for each subgroup of paths (ie, '/v1/images*')
		subRouter := mRouter.PathPrefix(api.GetPathPrefix()).Subrouter().StrictSlash(false)
		//apply middleware to that subgroup
		for _, middleware := range api.GetMiddleware() {
			subRouter.Use(middleware)
		}

		//add paths to subrouter
		for _, route := range api.Routes() {
			subRouter.
				Methods(route.Method, http.MethodOptions).
				Path(route.Pattern).
				Name(route.Name).
				Handler(route.HandlerFunc)

		}
	}

	mRouter.HandleFunc("/ping", HandlePing).Methods("GET")

	_ = mRouter.Walk(func(route *mux.Route, router *mux.Router, ancestors []*mux.Route) error {
		tmpl, err := route.GetPathTemplate()
		if err != nil {
			return err
		}
		fmt.Printf("route: %s, handler: %v\n", tmpl, route.GetHandler())
		return nil
	})
	return mRouter
}

func HandlePing(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
}

// EncodeJSONResponse uses the json encoder to write an interface to the http response with an optional status code
func EncodeJSONResponse(i interface{}, status *int, w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json; charset=UTF-8")
	if status != nil {
		w.WriteHeader(*status)
	} else {
		w.WriteHeader(http.StatusOK)
	}

	if err := json.NewEncoder(w).Encode(i); err != nil {
		return err
	}
	return nil
}

// custom encode function to not escape html
func EncodeTaggedHTMLJSONResponse(i interface{}, status *int, w http.ResponseWriter) {
	w.Header().Set("Content-Type", "application/json; charset=UTF-8")
	if status != nil {
		w.WriteHeader(*status)
	} else {
		w.WriteHeader(http.StatusOK)
	}
	buf := new(bytes.Buffer)
	enc := json.NewEncoder(buf)
	enc.SetEscapeHTML(false)
	err := enc.Encode(i)
	if err != nil {
		panic(err)
	}
	compactBuf := new(bytes.Buffer)
	err = json.Compact(compactBuf, buf.Bytes())
	if err != nil {
		panic(err)
	}
	_, err = w.Write(buf.Bytes())
	if err != nil {
		panic(err)
	}
}

// parseInt64Parameter parses a sting parameter to an int64
func parseInt64Parameter(param string) (int64, error) {
	return strconv.ParseInt(param, 10, 64)
}

// parseInt32Parameter parses a sting parameter to an int32
func parseInt32Parameter(param string) (int32, error) {
	val, err := strconv.ParseInt(param, 10, 32)
	if err != nil {
		return -1, err
	}
	return int32(val), nil
}
