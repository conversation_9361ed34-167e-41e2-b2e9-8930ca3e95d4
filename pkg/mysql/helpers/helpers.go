package helpers

import "strings"

// creates a string of len question marks, comma separated
func CreateSqlArgString(len int) string {
	qs := make([]string, len)
	for i := range qs {
		qs[i] = "?"
	}
	return strings.Join(qs, ",")
}

func ConvertToInterfaceSlice(args []string) []interface{} {
	newslice := make([]interface{}, len(args))
	for i := range args {
		newslice[i] = args[i]
	}
	return newslice
}
