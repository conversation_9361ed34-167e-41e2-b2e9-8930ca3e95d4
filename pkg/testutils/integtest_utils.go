//go:build integration
// +build integration

package testutils

import (
	"encoding/json"
	"log"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

type IntegTestConfig struct {
	SqlConnString    string `json:"sqlConnString"`
	AzStorageAccount string `json:"az_storage_account"`
	AzContainer      string `json:"az_container"`
}

func LoadTestConfigFromEnvVar(t *testing.T) IntegTestConfig {
	t.Helper()

	confPath, ok := os.LookupEnv("IT_CONF")

	if !ok {
		t.Fatal("can't setup integ test - config file not set.  set it with flag 'conf'.")
	}

	configJSON, err := os.ReadFile(confPath)
	if err != nil {
		log.Fatal("Unable to read config: ", err)
	}

	var conf IntegTestConfig
	err = json.Unmarshal(configJSON, &conf)
	if err != nil {
		log.Fatal("Unable to parse config")
	}

	return conf
}

func WDtoProjDir(t *testing.T) {
	projectDir := "reportprocessor"
	wd, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	for !strings.HasSuffix(wd, projectDir) {
		if wd == "/" {
			t.Fatal("could not locate project directory")
		}
		wd = filepath.Dir(wd)
	}
	err = os.Chdir(wd)
	if err != nil {
		t.Fatal(err)
	}

}
