package testutils

import (
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"testing"

	"github.com/sirupsen/logrus"
)

// LogrusJsonLogCatcher can be used to capture logs logged with logrus. It
// assumes logs are json
//
// Usage:
//
//	 func TestSomething(t *testing.T) {
//	    lc := LogrusJsonLogCatcher{Output: os.Stdout}
//		logrus.SetOutput(&lc)
//
//		// do some action that logs things
//		...
//
//		// check that a log with message "some message" and report_id=1 was logged
//		lc.AssertLogCaught("some message", "report_id", "1")
//	}
type LogrusJsonLogCatcher struct {
	logs []*logrus.Entry

	Output io.Writer
}

func (lc *LogrusJsonLogCatcher) Write(log []byte) (n int, err error) {
	entry, err := parseEntry(log)
	if err != nil {
		return 0, fmt.Errorf("failed parsing log as logrus.Entry: %w", err)
	}

	lc.logs = append(lc.logs, entry)

	if lc.Output == nil {
		return len(log), nil
	} else {
		return lc.Output.Write(log)
	}
}

func parseEntry(log []byte) (*logrus.Entry, error) {
	var j map[string]any
	err := json.Unmarshal(log, &j)
	if err != nil {
		return nil, fmt.Errorf("log is not valid json: %w", err)
	}

	return &logrus.Entry{
		Message: j["msg"].(string),
		Data:    j,
	}, nil
}

func (lc *LogrusJsonLogCatcher) AssertLogCaught(t *testing.T, msg string, fieldValues ...any) {
	if !lc.CaughtLog(msg, fieldValues...) {
		t.Logf(`No log with message~="%s" and fields %v found`, msg, fieldValues)
		t.Fail()
	}
}

func (lc *LogrusJsonLogCatcher) CaughtLog(msg string, fieldValues ...any) bool {
	for _, log := range lc.logs {
		msgMatches := strings.Contains(log.Message, msg)
		if msgMatches && expectedDataWasLogged(log, fieldValues...) {
			return true
		}
	}

	return false
}

func expectedDataWasLogged(log *logrus.Entry, fieldValues ...any) bool {
	if len(fieldValues)%2 != 0 {
		panic("invalid field values; expected key-value pairs but got odd number of arguments")
	}

	for i := 0; i < len(fieldValues); i += 2 {
		fieldName := fieldValues[i].(string)
		expectedValue := fieldValues[i+1]

		loggedData := log.Data[fieldName]
		if loggedData != expectedValue {
			return false
		}
	}
	return true
}
