package testutils

import (
	"golang.org/x/oauth2"
	"path/filepath"
	"strings"
)

type mockTokenSource struct {
	serviceTokenPath string
}

func (m mockTokenSource) Token() (*oauth2.Token, error) {
	token := "valid_token"

	return &oauth2.Token{AccessToken: token}, nil
}

func NewMockTokenSource() oauth2.TokenSource {
	path := "token.txt"
	return &mockTokenSource{
		serviceTokenPath: path,
	}
}

func FindProjectRoot(startDir, projectDirName string) string {
	for {
		if strings.HasSuffix(startDir, projectDirName) {
			return startDir
		}

		parentDir := filepath.Dir(startDir)
		if parentDir == startDir {
			break
		}
		startDir = parentDir
	}

	return ""
}
