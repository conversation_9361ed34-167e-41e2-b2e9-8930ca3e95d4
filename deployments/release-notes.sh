#!/bin/bash
#get current and second-most current tag
CURR_TAG="$(git describe --tags `git rev-list --tags --max-count=1`)"
LAST_TAG="$(git describe --abbrev=0 --tags $CURR_TAG^)"
#get commit messages between those two tags and link to the associated Jira tasks
REL_NOTES="$(git log --pretty='%s' $LAST_TAG..$CURR_TAG | sed -E 's/"/\\\"/g')"
#format into nice slack message
JSON_MESSAGE="{\"type\":\"mrkdwn\", \"text\":\"*Report Processor release $CURR_TAG*\n\n\n$REL_NOTES\"}"
curl -X POST -H 'Content-type: application/json' --data "$JSON_MESSAGE" *******************************************************************************