apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-write-timeout: "300"
    nginx.ingress.kubernetes.io/service-upstream: "true"
  name: reportprocessor
  namespace: default
spec:
  ingressClassName: nginx-internal
  rules:
    - host: reportprocessor.cactrl.qa.pocket.health
      http:
        paths:
          - backend:
              service:
                name: reportprocessor
                port:
                  number: 7443
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - reportprocessor.cactrl.qa.pocket.health
      secretName: reportprocessor-tls
