#!/bin/bash
set -e
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
if [[ "$1" == "-h" || $# -ne 3 ]]; then
    echo "Usage: provision.sh paramdir rg mc-rg"
    echo 
    echo "  Provision report processor service keyvault, sql.  Requires that"
    echo "  az client be installed and logged in with resource creation privileges in the given"
    echo "  resource groups.  You will be prompted for mysql password for sql provisioning."
    echo 
    echo "  paramdir: path to stack parameters"
    echo "  rg: resource group of stack"
    echo "  mc-rg: MC resource group of stack's AKS"
    echo
    echo "  Example: ./provision.sh qa-global pockethealth_test MC-qa-aks-2"
    exit 1
fi

paramdir=$1
rg=$2
mcrg=$3

mkdir -p out

# run arm templates
az deployment group create \
	--name provision-rp-id \
	--resource-group "$mcrg" \
	--template-file tmpl/id.json \
    --parameter "params/$paramdir/id.param.json" \
    | tee out/id.out.json
az deployment group create \
	--name provision-rp-secrets \
	--resource-group $rg \
	--template-file tmpl/keyvault_secrets.json \
	--parameter "params/$paramdir/keyvault_secrets.param.json" \
    | tee out/kv.json

# fill in k8s template
sed -e "s#__AZ_ID_RESOURCE_ID__#$(jq -r .properties.outputs.resourceID.value < out/id.out.json)#" \
  -e "s#__AZ_ID_CLIENT_ID__#$(jq -r .properties.outputs.clientID.value < out/id.out.json)#" k8s/k8s.tmpl.yml > out/k8s.tmpl.yml

echo "Provisioning done.  Outputs for ARM deployments in out/.  K8s objects for Managed ID and Service are in out/k8s.tmpl.yml."
