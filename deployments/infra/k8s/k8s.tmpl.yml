apiVersion: "aadpodidentity.k8s.io/v1"
kind: AzureIdentity
metadata:
  name: rp-podid
spec:
  type: 0
  resourceID: __AZ_ID_RESOURCE_ID__
  clientID: __AZ_ID_CLIENT_ID__
---
apiVersion: "aadpodidentity.k8s.io/v1"
kind: AzureIdentityBinding
metadata:
  name: rp-podid-binding
spec:
  azureIdentity: rp-podid
  selector: rp-podid
---
apiVersion: v1
kind: Service
metadata:
  name: reportprocessor
spec:
  type: ClusterIP
  selector:
    app: reportprocessor
  ports:
    - protocol: TCP
      port: 7443
      targetPort: 7443

