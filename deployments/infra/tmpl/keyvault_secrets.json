{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"ph_env": {"type": "string", "metadata": {"description": "PocketHealth environment (qa or prod)"}}, "ph_stack_name": {"type": "string", "metadata": {"description": "PocketHealth stack name (i.e. uswest)"}}, "keyVaultName": {"type": "string", "defaultValue": "[concat('ph-kv-', parameters('ph_env'), '-', parameters('ph_stack_name'))]", "metadata": {"description": "Specifies the name of the key vault."}}, "sql-conn-string": {"type": "securestring", "metadata": {"description": "Specifies the value of the secret that you want to create."}}, "azstore-key": {"type": "securestring", "metadata": {"description": "Specifies the value of the secret that you want to create."}}}, "resources": [{"type": "Microsoft.KeyVault/vaults/secrets", "apiVersion": "2019-09-01", "name": "[concat(parameters('keyVaultName'), '/', 'rp-sql-conn-str')]", "location": "[resourceGroup().location]", "properties": {"value": "[parameters('sql-conn-string')]"}}, {"type": "Microsoft.KeyVault/vaults/secrets", "apiVersion": "2019-09-01", "name": "[concat(parameters('keyVaultName'), '/', 'rp-azstore-key')]", "location": "[resourceGroup().location]", "properties": {"value": "[parameters('azstore-key')]"}}], "outputs": {"sql_sec_name": {"type": "string", "value": "[concat('rp-sql-conn-str')]"}, "azstore_key_sec_name": {"type": "string", "value": "[concat('rp-azstore-key')]"}}}