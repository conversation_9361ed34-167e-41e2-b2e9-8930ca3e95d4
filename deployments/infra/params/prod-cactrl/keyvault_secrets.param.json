{
    "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
    "contentVersion": "1.0.0.0",
    "parameters": {
      "ph_env": {
        "value": "prod"
      },
      "ph_stack_name": {
        "value": "cactrl"
      },
      "keyVaultName": {
        "value": "phprodkvcacentral0" //This is needed because we want to deploy to our existing stack, which doesn't follow the new IaC naming conventions yet
      }
    }
  }
  
