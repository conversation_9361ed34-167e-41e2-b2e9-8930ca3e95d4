# Backend Infrastructure

Welcome to Report Processor Infrastructure-as-Code (IaC).  This directory contains automation for bringing up infrastructure for Report Processor in the form of Azure Resource Manager (ARM) templates, scripts, and documentation.

Directory structure:

- `tmpl/` - ARM templates for backend infrastructure
- `param/` - Parameters for ARM templates
- `helpers/` - Helper scripts
- `k8s/` - Template for K8s objects
- `out/` - Created by provision script; contains result of the provisioning

The organization of ARM templates follows the patterns and naming conventions from PocketHealth Backend Infrastructure. 

# Prerequisite

The following sections presume that:

1. You have Azure CLI client (`az`), logged in with permissions for creating resources and running ARM templates
1. You have some basic understanding of ARM templates (what they are, how to run them, basics of how to read them / get help for them) -- see References at bottom of doc for further ARM info.

# Usage

For a new stack, need the following:

1. Managed Identity
1. Keyvault
1. Database

## Preamble

The rest of the doc describes the steps to create and configure the infrastructure for Report Processor.  You will need the following info from the backend stack to which you want to deploy the Report Processor infrastructure:

- `${ENV}` - environment: prod or qa
- `${NAME}` - name of the stack; usually named after a geographical region, i.e. uswest, or cactrl.
- `${RG}` - Resource Group containing the backend infra
- `${MC_RG}` - The Resource Group – used by the AKS cluster for the stack (name usually begins with `MC-`)

## Provisioning script

The `provision.sh` script will run all of the template and helper scripts needed to set up everything except sql db creation.

```
Usage: provision.sh paramdir rg mc-rg mysqlhost mysqluser destkv

  Provision Report Processor service keyvault, sql.  Requires that
  az client be installed and logged in with resource creation privileges in the given
  resource groups.  You will be prompted for mysql password for sql provisioning.

  paramdir: path to stack parameters
  rg: resource group of stack
  mc-rg: MC resource group of stack's AKS

  Example: ./provision.sh qa-global pockethealth_test MC-qa-aks-2
```

During execution of the script, you will be prompted for some information.  You should be ready to provide the following information:

- SQL connection string: this is the connection string for RP to use.  Creation of the SQL authentication is not autmated yet, but you can use the `helpers/gen-sql-pw.sh` script to generate a password and get the GRANT commands.

- Blob store key: this is what the service uses to authenticate access to the azure blob store
