#!/bin/bash

pw_len=${1:-24}

pw=$(LC_ALL=C tr -dc 'A-Za-z0-9!#$%&()*+,-./;<=>?^_~' </dev/urandom | head -c "$pw_len" && echo)

echo "Password: ${pw}"
echo "- connection string format: <user>:${pw}@tcp(<host>)/<dbname>?parseTime=true&tls=skip-verify&allowNativePasswords=true" 
echo "- if using Azure MySQL, <user> needs to be <user>@<servername>"
echo "GRANT commands: "
echo "  CREATE USER IF NOT EXISTS '<user>'@'%' IDENTIFIED BY '${pw}';"
echo "  GRANT ALL PRIVILEGES ON <dbname>.* TO '<user>'@'%';"
