#!/bin/bash

pw_len=${1:-64}

apikey=$(LC_ALL=C tr -dc 'A-Za-z0-9' </dev/urandom | head -c "$pw_len" && echo)
echo "API key: (to be be stored in keyvault for core/whatever accessing service)"
echo "${apikey}"
hash=$(echo -n "$apikey" | openssl dgst -sha256 -binary | xxd -p -c 256)
echo "Hash SQL insert: (replace <type> with role type. Ie, 1 = Read, 2 = Write, 3=Read/Write"
echo "INSERT INTO auth_keys (name, key_hash, type) VALUES (<name>, '$hash', <type>) "
