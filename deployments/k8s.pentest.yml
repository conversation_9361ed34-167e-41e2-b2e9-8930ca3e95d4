apiVersion: v1
kind: Service
metadata:
  name: reportprocessor
  namespace: pentest-2024
spec:
  type: ClusterIP
  selector:
    app: reportprocessor
  ports:
    - protocol: TCP
      port: 7443
      targetPort: 7443
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: reportprocessor
  namespace: pentest-2024
  labels:
    app: reportprocessor
spec:
  replicas: 1
  progressDeadlineSeconds: 600 
  selector:
    matchLabels:
      app: reportprocessor
  template:
    metadata:
      name: reportprocessor
      labels:
        app: reportprocessor            
        azure.workload.identity/use: "true"
    spec:
      serviceAccountName: sa-reportprocessor
      securityContext:
        runAsUser: 21000
        runAsGroup: 21000
      containers:
      - name: reportprocessor
        image: phcrcacentral0.azurecr.io/reportprocessor@sha256:733334042152a1f95bfe846c0bbdc13899406bae9846cee0b1f75e71c13441ce
        ports:
          - containerPort: 7443
        imagePullPolicy: Always
        command: [ "./docker-entrypoint.sh", "./reportprocessor", "--env", "pentest" ]
        readinessProbe:
          httpGet:
            path: /ping
            port: 7443
            scheme: HTTP
          timeoutSeconds: 2
          failureThreshold: 5
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 250m
            memory: 768Mi
---
apiVersion: v1
kind: ServiceAccount
metadata:
    annotations:
        azure.workload.identity/client-id: c390efab-eff9-4f56-a9c7-42b3451e22fb
    name: sa-reportprocessor
    namespace: pentest-2024


