#!/bin/bash

set -e

# dir of this script; borrowed from S.O.: 
# https://stackoverflow.com/questions/59895/how-to-get-the-source-directory-of-a-bash-script-from-within-the-script-itself
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"

source "$SCRIPT_DIR/tmp/config.sh"

echo "turn off MySQL strict mode"
MYSQL_PWD=rootpw mysql -h "$sql_host" --port "$sql_port" -u root -e "SET GLOBAL sql_mode = 'NO_ENGINE_SUBSTITUTION';" 

#run goose up to create all tables
cd ../ && make db_up

echo "adding sql test data..."
for file in $(ls "$SCRIPT_DIR/sqldata/"); do
	echo "-- $file"
	set +e 
	cat "$SCRIPT_DIR/sqldata/$file" | MYSQL_PWD=$sql_pw mysql -h "$sql_host" --port "$sql_port" --user "$sql_user" --database reportprocessor
	set -e
done




