version: '3.1'

services:

  mysql:
    image: phcrcacentral0.azurecr.io/mysql:5.7.30
    command: --disable-partition-engine-check
    platform: linux/x86_64
    restart: always
    container_name: apptest-mysql-1
    environment:
      MYSQL_DATABASE: reportprocessor
      MYSQL_ROOT_PASSWORD: rootpw
      MYSQL_USER: devuser
      MYSQL_PASSWORD: devpw
    ports:
      - 3306:3306
    networks:
      - apptest

networks:
  apptest:
    driver: bridge
