.PHONY: setup_dev_deps apptest_dev clean

PH_TEST_ENV ?= dev

# start set up local dependencies and run app tests.  dependencies will be 
# torn down if everything successful.
apptest_dev: 
	mkdir -p tmp
ifeq ($(PH_TEST_ENV),dev)	
	./start-svc.sh $(PH_TEST_ENV)
endif
	go test ./... -v --tags=apptest -host=https://$(shell grep sut_host config.$(PH_TEST_ENV).sh | cut -d= -f2 | tr -d \") -env=$(PH_TEST_ENV)
ifeq ($(PH_TEST_ENV),dev)	
	./stop-dev-deps.sh
endif

# start and set up local dependenciens (can use for local development)
setup_dev_deps: 
	mkdir -p tmp
	cp -r config.$(PH_TEST_ENV).sh tmp/config.sh
ifeq ($(PH_TEST_ENV),dev)	
	./start-dev-deps.sh
	./setup-test-data.sh
endif

# resets the state of app tests and tears down all local dependencies
clean:
	./stop-dev-deps.sh
	rm -rf tmp out
