#!/bin/bash
set -e

if [[ $# != "1" ]]; then
	echo "usage: ./start-svc.sh [dev | qa | prod]"
	exit 1
fi
TEST_ENV=$1

# dir of this script; borrowed from S.O.: 
# https://stackoverflow.com/questions/59895/how-to-get-the-source-directory-of-a-bash-script-from-within-the-script-itself
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"

mkdir -p tmp
log="$SCRIPT_DIR/tmp/reportprocessor.out"

set +e
echo "Will start service and check if it is healthy"
cd ..

if [[ "$TEST_ENV" == "dev" ]]; then
	container_id=$(docker run -d --user="21000:21000" -p 443:20443 --network="apptest_apptest" phcrcacentral0.azurecr.io/reportprocessor:latest $1)
else
	container_id=$(docker run -d --user="21000:21000" -p 443:20443  phcrcacentral0.azurecr.io/reportprocessor:latest $1)
fi 

if [[ $? != "0" ]]; then
	echo "Failed to start reportprocessor"
	exit 1
fi

sleep 5

if [[ $(docker ps -q --filter "id=$container_id" --filter "status=running") == "" ]] ; then
	echo "Service seems to have died.  Logs: $log"
	exit 1
fi

if ! docker logs $container_id 2>&1 | grep -q "Listening..."; then
	echo "Service running but did not find listening message.  Will kill.  Logs: $log"
	docker kill $container_id
	exit 1
fi

echo $container_id > "$SCRIPT_DIR/tmp/ps_pid"
echo "Service started in docker container $container_id"
