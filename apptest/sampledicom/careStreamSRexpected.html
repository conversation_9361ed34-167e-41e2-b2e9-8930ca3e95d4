<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title></title>
<style type="text/css">
    @media screen {
    div.printonly {display: none;}
    }

    @media print {
    div.screenonly {display: none;}
    }
    html, body {
    margin: 0;
    padding: 0;
    font-family: Arial;
    color: #494949;
    font-size:13px;
    }

    a {
    color: #009abd;
    }

    #Page table td {
    /*font-size: 0px;*/
    }

    table td span, table td div {
    font-size: 12px;
    }

    #Header {
    text-align: center;
    }

    #HeaderStrip {
    background-color: #2a5572;
    border-bottom: 2px solid #ee7e15;
    text-align: center;
    }

    #HeaderLogosTable {
    height: 78px;
    width: 740px;
    margin: auto;
    color: #fff;
    }

    #HeaderLogosTable span {
    font-size: 18px;
    }

    #HeaderLogosTable #LeftLogo {
    text-align: left;
    }

    #HeaderLogosTable #RightLogo {
    text-align: right;
    }

    #HeaderLogosTable #RightLogo img {
    border-width: 0px;
    }

    #ReportInformationBox {
    width: 740px;
    margin: auto;
    }

    #Page {
    text-align: center;
    margin: auto;
    }

    #PageMainTitle {
    width: 740px;
    margin: auto;
    }

    .rowSpace {
    height:20px;
    font-size: 0px;
    }

    .rowDoubleSpace {
    height:40px;
    font-size: 0px;
    }

    .boxTitleContainer,
    .boxTitleContainerStudy {
    width:100%;
    color: #fff;
    text-align:left;
    background-color:#40a3d7;
    }

    .boxTitleContainer td {
    font-size: 16px;
    background-color:#40a3d7;
    width:100%;
    height:27px;
    padding-left:10px;
    width:100%;
    }

    .boxTitleContainerStudy {
    width:100%;
    background-color:#ee7e15;
    }

    .boxTitleContainerStudy td {
    font-size:18px;
    font-weight:bold;
    background-color:#ee7e15;
    height: 37px;
    width:100%;
    padding-left:10px;
    }

    .subTitle {
    height: 31px;
    }

    .subTitle table {
    background-color: #f0f1f2;
    border: 1px solid #d6d6d6;
    text-align: left;
    width: 100%;
    height: 95%;
    }

    .subTitle table td {
    padding-left: 5px;
    height:31px;
    }

    .subTitle table td span {
    font-size: 13px;
    }

    .detailsTable {
    text-align: left;
    width: 97%;
    margin-left: 6px;
    }

    .detailsTable tr {
    height: 20px;
    }

    .detailsTable td {
    vertical-align: top;
    }

    .detailsTable td span {
    font-size: 13px;
    }

    .horizontalSeperatorContainer {
    background-color: #d6d6d6;
    font-size: 0px;
    }

    .horizontalSeperator {
    background-color: #d6d6d6;
    width: 1px;
    height: 1px;
    overflow: hidden;
    font-size:0px;
    }

    #GraphBoxesContainerDiv {
    width: 740px;
    overflow: hidden;
    }

    #GraphBoxesContainer {
    width: 838px;
    }

    .graphBoxContainer {
    float: left;
    margin-right: 9px;
    width: 364px;
    }

    .graphBox {
    border: 1px solid #d6d6d6;
    }

    .graphBox img {
    width:359px;
    }

    .dataTable {
    width: 100%;
    text-align: left;
    border-left: 1px solid #d6d6d6;
    border-right: 1px solid #d6d6d6;
    }

    .headerRow {
    background-color: #c7c8c9;
    }

    .dataTable .headerRow {
    background-color: #c7c8c9;
    height: 40px;
    }

    .headerRow th {
    background-color: #c7c8c9;
    text-align:left;
    }

    .dataTable th, .dataTable td {
    border-top: 1px solid #d6d6d6;
    }

    .row td,
    .altRow td,
    .headerRow th {
    padding: 5px 10px;
    }

    .dataTable th {
    vertical-align: top;
    border-left: 1px solid #c7c8c9;
    text-align:left;
    }

    .dataTable th span {

    }

    .dataTable th.leftBorders {
    border-left: 1px solid #fff;
    }

    .dataTable td span {
    font-size: 13px;
    }

    .altRow {
    background-color: #f0f1f2;
    }

    .altRow td {
    background-color: #f0f1f2;
    }

    .dataTable td.noPadding {
    padding: 0px;
    }

    .cellType1 table {
    background-color: #f0f1f2;
    width:100%;
    border-left: 1px solid #d6d6d6;
    border-bottom: 1px solid #d6d6d6;
    }

    .cellType2 table {
    background-color: #f4f5f6;
    width:100%;
    border-left: 1px solid #d6d6d6;
    border-bottom: 1px solid #d6d6d6;
    }

    .cellType3 table {
    background-color: #f7f8f8;
    width:100%;
    border-left: 1px solid #d6d6d6;
    border-bottom: 1px solid #d6d6d6;
    }

    .cellType4 table {
    background-color: #f8f8f8;
    width:100%;
    border-left: 1px solid #d6d6d6;
    border-bottom: 1px solid #d6d6d6;
    }

    .cellType1 table td,
    .cellType2 table td,
    .cellType3 table td,
    .cellType4 table td {
    height:34px;
    }

    .dataTable .currentColumn td {
    font-weight: bold;
    }

    .dataTable .currentColumn .cellType1 table {
    background-color: #d8eaf4;
    border-left-color: #5fb6e1;
    border-bottom-color: #5fb6e1;
    }

    .dataTable .currentColumn .cellType2 table {
    background-color: #e4f0f7;
    border-left-color: #80c5e7;
    border-bottom-color: #80c5e7;
    }

    .dataTable .currentColumn .cellType3 table {
    background-color: #ebf4f9;
    border-left-color: #a2d5ed;
    border-bottom-color: #a2d5ed;
    }

    .dataTable .currentColumn .cellType4 table {
    background-color: #f3f9fc;
    border-left-color: #d7edf7;
    border-bottom-color: #d7edf7;
    }

    .dataTable .cellType1 table, .dataTable .cellType2 table, .dataTable =
.cellType3 table, .dataTable .cellType4 table {
    display: inline-block;
    }

    .dataTable .cellType1 td, .dataTable .cellType2 td, .dataTable =
.cellType3 td, .dataTable .cellType4 td {
    font-size: 13px !important;
    padding: 0px;
    border-top-width: 0px;
    text-align: left;
    }

    .dataTable .cellType1 td.middleCell, .dataTable .cellType2 =
td.middleCell, .dataTable .cellType3 td.middleCell, .dataTable .cellType4 =
td.middleCell {
    width: 65px;
    text-align: right;
    }

    .dataTable .cellType1 td.middleEmptyCell, .dataTable .cellType2 =
td.middleEmptyCell, .dataTable .cellType3 td.middleEmptyCell, .dataTable =
.cellType4 td.middleEmptyCell {
    padding-left: 5px;
    }

    .dataTable .cellType1 td.imageCell, .dataTable .cellType2 td.imageCell,=
 .dataTable .cellType3 td.imageCell, .dataTable .cellType4 td.imageCell {
    width: 18px;
    text-align: center !important;
    }

    .dataTable .cellType1 td.valueCell, .dataTable .cellType2 td.valueCell,=
 .dataTable .cellType3 td.valueCell, .dataTable .cellType4 td.valueCell {
    width: 40px;
    text-align: center !important;
    }

    .footerRow {
    width: 100%;
    background-color: #d8eaf4;
    border: 1px solid #6eb6da;
    text-align: left;
    color: #252525;
    }

    .footerRow td {
    padding: 7px 10px;
    border-bottom: 1px solid #6eb6da;
    border-top: 1px solid #6eb6da;
    }

    .footerRowNoBorder td {
    padding: 0px 0px;
    border-bottom: 0px solid #6eb6da;
    border-top: 0px solid #6eb6da;
    font-weight:bold;
    font-size:14px;
    }

    /* CSS3 Only */
    .footerRow td:first-child {
    border-left: 1px solid #6eb6da;
    }

    .footerRow td:last-child {
    border-right: 1px solid #6eb6da;
    }
    /* CSS3 Only */

    .footerRow td span {
    font-size: 14px;
    }

    .warningText {
    font-size: 11px;
    text-align: left;
    padding: 0px;
    margin: 0px;
    }

    .warningText .red {
    color: #ff0000;
    }

    #LesionsTable td {
    vertical-align: top;
    }

    #FindingOverTimeTable td {
    white-space: nowrap;
    text-align:left;
    }

    #Footer {
    text-align: center;
    padding-bottom: 10px;
    }

    #Footer table {
    width: 740px;
    margin: auto;
    text-align: left;
    font-size: 10px;
    }

    #Footer table span {
    font-size: 10px;
    }

    #Footer .title {
    font-size: 11px;
    font-weight: bold;
    margin-bottom: 5px;
    }

    #Footer .seperator {
    height: 1px;
    font-size: 0px;
    background-color: #eee;
    margin-top: 10px;
    margin-bottom: 10px;
    }

    /* Helpers
    -------------*/
    sup {
    font-size:10px;
    }

    .bold {
    font-weight: bold;
    }

    .leftAlign {
    text-align: left !important;
    }

    .leftAlignBold {
    font-weight: bold;
    text-align: left !important;
    }

    .clear {
    clear: both;
    }
  </style>
</head>
<body>
<!--Reason for version creation: RIS-->
<dl>&nbsp;
<center>
<font style="FONT-SIZE: 18px; COLOR: #330099; FONT-FAMILY:Arial">JEROLD&nbsp;&nbsp;PHELPS&nbsp;&nbsp;COMMUNITY&nbsp;&nbsp;HOSPITAL</font><br><br>
<table CELLSPACING="0" CELLPADDING="0" WIDTH="354" height="68" style="font-family: Arial;" id="tblReportHeadLine">
<tr><td width="30px">&nbsp;</td>
<td align=center>
<font size="+3"><b>Radiology Report</b></font>
</td>
</tr>
</table>
</center>
<br>
<br>
<table align="center" width="98%" id="tblReportPatientDetails" style="font-family: Arial;font-size:12px">
<tr>
<td width="24%" bgcolor="WhiteSmoke"><b>&nbsp;&nbsp;Patient Name:</b></td>
<td width="24%" bgcolor="AliceBlue">&nbsp;&nbsp;  </td>
<td width="4%"></td>
<td width="24%" bgcolor="WhiteSmoke"><b>&nbsp;&nbsp;Report Date:</b></td>
<td width="24%" bgcolor="AliceBlue">&nbsp;&nbsp;</td>
</tr>
<tr>
<td width="24%" bgcolor="WhiteSmoke"><b>&nbsp;&nbsp;Patient ID:</b></td>
<td width="24%" bgcolor="AliceBlue">&nbsp;&nbsp;</td>
<td width="4%"></td>
<td width="24%" bgcolor="WhiteSmoke"><b>&nbsp;&nbsp;Accession No.:</b></td>
<td width="24%" bgcolor="AliceBlue">&nbsp;&nbsp;</td>
</tr>
 <tr>
<td width="24%" bgcolor="WhiteSmoke"><b>&nbsp;&nbsp;Patient Birth Date:</b></td>
<td width="24%" bgcolor="AliceBlue">&nbsp;&nbsp;</td>
<td width="4%"></td>
<td width="24%" bgcolor="WhiteSmoke"><b>&nbsp;&nbsp;Report Status:</b></td>
<td width="24%" bgcolor="AliceBlue">&nbsp;&nbsp;F</td>
</tr>
<tr>
<td width="24%" bgcolor="WhiteSmoke"><b>&nbsp;&nbsp;Referring Physician:</b></td>
<td width="24%" bgcolor="AliceBlue">&nbsp;&nbsp;</td>
<td width="4%"></td>
<td width="24%" bgcolor="WhiteSmoke"><b>&nbsp;&nbsp;Reason For Study:</b></td>
<td width="24%" bgcolor="AliceBlue">&nbsp;&nbsp;hip pain</td>
</tr>
</table>
</dl>

<br>
<!--RIS report date and time is: 20210428121439.000000--><BR>Clinical History/Indication for Exam:

<BR>Rt hip pain x>1 week. pain is constant, aching pain with periods of sharp nerve type surges of pain. rt hip pain with and without bearing weight.

<BR>
<BR>CT PELVIS WITHOUT INTRAVENOUS CONTRAST

<BR>
<BR>INDICATION:  Rt hip pain x>1 week. pain is constant, aching pain with

<BR>periods of sharp nerve type surges of pain. rt hip pain with and

<BR>without bearing weight.

<BR>
<BR>TECHNIQUE:  Axial computed tomography images of the pelvis without

<BR>intravenous contrast.  Sagittal and coronal reformatted images were

<BR>created and reviewed.  This CT exam was performed using one or more of

<BR>the following dose reduction techniques:  automated exposure control,

<BR>adjustment of the mA and/or kV according to patient size, and/or use

<BR>of iterative reconstruction technique.

<BR>
<BR>COMPARISON:  No relevant prior studies available.

<BR>
<BR>FINDINGS:

<BR>
<BR>Bowel:  Visualized small and large bowel is unremarkable.  No mucosal

<BR>thickening.

<BR>
<BR>Appendix:  Normal appendix.

<BR>
<BR>Intraperitoneal space:  Unremarkable.  No free air.  No significant

<BR>fluid collection.

<BR>
<BR>Bladder:  Unremarkable.  No stones.

<BR>
<BR>Reproductive:  No uterine or adnexal enlargement is seen, previous

<BR>hysterectomy has been performed.

<BR>
<BR>Bones/joints:  Normal appearance of the hip joint space bilaterally.

<BR>No fractures of the hips or pelvis.  Symmetric appearance of the

<BR>pelvis.  Remaining joint spaces appear to be maintained.  Symmetric

<BR>appearance of the sacrum.  The lower lumbar spine in the field-of-view

<BR>is unremarkable.  No dislocation.

<BR>
<BR>Soft tissues:  Unremarkable.

<BR>
<BR>Vasculature:  Vascular calcifications of the aorta and iliac arteries

<BR>are noted.  No lower abdominal aortic aneurysm.

<BR>
<BR>Lymph nodes:  Unremarkable.  No enlarged lymph nodes.

<BR>
<BR>IMPRESSION:

<BR>1.  No acute findings are seen.  No fractures.  Normal joint spaces of

<BR>the hips bilaterally.

<BR>2.  Further imaging workup with CT or MRI examination may be helpful

<BR>if clinically indicated.

<BR>
<BR>
<BR>
<BR>Automatic exposure control was used as a dose lowering technique.

<BR>
<BR>Radiation Dose: CTDI is 5.76 mGy. DLP is 181.23 mGy-cm.

<BR>
<BR>** REPORT SIGNATURE ON FILE  00/00/0000 (00:00 Pacific Time ) **

<BR>Signed by: test Doctor, M.D.

<center>
<hr NOSHADE WIDTH="100%"></center>
<b><font face="Arial,Helvetica"><font size=-1>Medical Disclaimer</font></font></b>
<p><font face="Arial,Helvetica" size=-2>The images included in this
E-Mail Module message are compressed reproductions of key images of a complete
case study, which, though viewable, are not of diagnostic quality and are
not designed nor intended to replace original images. The compressed images
included in this E-Mail Module message are not designed nor intended to become
a permanent part of any patient's medical record. Original images of the
full case study such as X-Rays, MRI's, CT scans, and other diagnostic materials,
should be reviewed by the user or other medical professionals for a final
diagnosis and determination of medical care or treatment.
<br>Carestream Health expressly disclaims
any and all liability for injury of any kind (including economic injury
or injury to person or property) and for damages of any kind (including
direct, incidental, and consequential damages) resulting directly or indirectly
from the use of any information, images, or other materials contained in
the E-Mail Module message.</font></p>
<p><b><font face="Arial,Helvetica"><font =
size=-1>Confidentiality</font></font></b>
<p><font face="Arial,Helvetica"><font size=-2>The information contained
in this E-Mail Module message is CONFIDENTIAL PATIENT MEDICAL INFORMATION
and, as a user of this E-Mail Module message, you agree to treat the information
as such, in accordance with all applicable state and federal laws and regulations
governing the use and protection of such CONFIDENTIAL PATIENT MEDICAL INFORMATION.
Carestream Health is not responsible for and hereby disclaims all responsibility and liability for the subsequent use and continued protection of CONFIDENTIAL PATIENT MEDICAL INFORMATION provided to, or viewed by users of this E-Mail Module message.</font></font></p>
<hr NOSHADE WIDTH="100%">
<b><font face="Arial,Helvetica"><font size=-1>Produced by E-Mail Module, Carestream Health product</body>
</html>
