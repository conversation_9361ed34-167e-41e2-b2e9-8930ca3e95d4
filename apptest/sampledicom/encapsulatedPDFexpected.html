<!DOCTYPE html>
<!-- Created by pdf2htmlEX (https://github.com/coolwanglu/pdf2htmlex) -->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta charset="utf-8"/>
<meta name="generator" content="pdf2htmlEX"/>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
<style type="text/css">
/* vim: set shiftwidth=2 tabstop=2 autoindent cindent expandtab filetype=css: */
/*! 
 * Base CSS for pdf2htmlEX
 * Copyright 2012,2013 Lu Wang <<EMAIL>> 
 * https://github.com/coolwanglu/pdf2htmlEX/blob/master/share/LICENSE
 */
/* Part 1: Web Page Layout: Free to modify, except for a few of them which are required by pdf2htmlEX.js, see the comments */
#sidebar { /* Sidebar */
  position:absolute;
  top:0;
  left:0;
  bottom:0;
  width:250px;
  padding:0;
  margin:0px;
  overflow:auto;
}
#page-container { /* PDF container */
  position:absolute; /* required for calculating relative positions of pages in pdf2htmlEX.js */
  top:0;
  left:0px;
  margin:0; 
  padding:0;
  border:0; /* required for lazy page loading in pdf2htmlEX.js (page visibility test) */
}
@media screen {
  /* for sidebar */
  #sidebar.opened + #page-container { left:250px; }
  #page-container {
    /* `bottom' and `right' are required for lazy page loading in pdf2htmlEX.js (page visibility test)
     * alternatively you may set width and height
     */
    bottom:0;
    right:0;
    overflow:auto;
  }
  .loading-indicator {
    display:none;
  }
  .loading-indicator.active {
    display:block;
    position:absolute;
    width:64px;
    height:64px;
    top:50%;
    left:50%;
    margin-top:-32px;
    margin-left:-32px;
  }
  .loading-indicator img {
    position:absolute;
    top:0;
    left:0;
    bottom:0;
    right:0;
  }
}
@media print { 
  @page { margin:0; }
  html { margin:0; }
  body { 
    margin:0; 
    -webkit-print-color-adjust:exact; /* enable printing background images for WebKit */
  }
  #sidebar { display:none; }
  #page-container {
    width:auto;
    height:auto;
    overflow:visible;
    background-color:transparent;
  }
  .d { display:none; }
}
/* Part 2: Page Elements: Modify with caution
 * The followings are base classes, some of which are meant to be override by PDF specific classes
 * So do not increase the specificity (e.g. ".classname" -> "#page-container .classname")
 */
.pf { /* page */
  position:relative;
  background-color:white;
  overflow: hidden;
  margin:0; 
  border:0; /* required by pdf2htmlEX.js for page visibility test */
}
.pc { /* content of a page */
  position:absolute;
  border:0;
  padding:0;
  margin:0;
  top:0;
  left:0;
  width:100%;
  height:100%;
  overflow:hidden;
  display:block;
  /* set transform-origin for scaling */
  transform-origin:0% 0%;
  -ms-transform-origin:0% 0%;
  -webkit-transform-origin:0% 0%;
}
.pc.opened { /* used by pdf2htmlEX.js, to show/hide pages */
  display:block;
}
.bf { /* images that occupies the whole page */
  position:absolute;
  border:0;
  margin:0;
  top:0;
  bottom:0;
  width:100%;
  height:100%;
  -ms-user-select:none;
  -moz-user-select:none;
  -webkit-user-select:none;
  user-select:none;
}
.bi { /* images that cover only a part of the page */
  position:absolute;
  border:0;
  margin:0;
  -ms-user-select:none;
  -moz-user-select:none;
  -webkit-user-select:none;
  user-select:none;
}
@media print {
  .pf {
    margin:0;
    box-shadow:none;
    page-break-after:always;
    page-break-inside:avoid;
  }
  @-moz-document url-prefix() {
    /* fix page truncation for FireFox */
    .pf {
      overflow:visible;
      border:1px solid #FFFFFF;
    }
    .pc {overflow:visible;}
  }
}
.c { /* clip box */
  position:absolute;
  border:0;
  padding:0;
  margin:0;
  overflow:hidden;
  display:block;
}
.t { /* text line */
  position:absolute;
  white-space:pre;
  font-size:1px;
  transform-origin:0% 100%;
  -ms-transform-origin:0% 100%;
  -webkit-transform-origin:0% 100%;
  unicode-bidi:bidi-override;/* For rtl languages, e.g. Hebrew, we don't want the default Unicode behaviour */
  -moz-font-feature-settings:"liga" 0;/* We don't want Firefox to recognize ligatures */
}
.t:after { /* webkit #35443 */
  content: '';
}
.t:before { /* Workaround Blink(up to 41)/Webkit bug of word-spacing with leading spaces (chromium #404444 and pdf2htmlEX #412) */
  content: '';
  display: inline-block;
}
.t span { /* text blocks within a line */
  /* Blink(up to 41)/Webkit have bug with negative word-spacing and inline-block (pdf2htmlEX #416), so keep normal span inline. */
  position:relative;
  unicode-bidi:bidi-override; /* For rtl languages, e.g. Hebrew, we don't want the default Unicode behaviour */
}
._ { /* text shift */
  /* Blink(up to 41)/Webkit have bug with inline element, continuous spaces and word-spacing. Workaround by inline-block. */
  display: inline-block;
  color: transparent;
  z-index: -1;
}
/* selection background should not be opaque, for fallback mode */
::selection{
  background: rgba(127,255,255,0.4);
}
::-moz-selection{
  background: rgba(127,255,255,0.4);
}
.pi { /* info for Javascript */
  display:none;
}
.l { /* annotation links */
}
/* transparent color - WebKit */
.d { /* css drawing */
  position:absolute;
  transform-origin:0% 100%;
  -ms-transform-origin:0% 100%;
  -webkit-transform-origin:0% 100%;
}
/* for the forms */
.it {
  border: none;
  background-color: rgba(255, 255, 255, 0.0);
}

.ir:hover {
  cursor: pointer;
}

/* Base CSS END */
</style>
<style type="text/css">
/* vim: set shiftwidth=2 tabstop=2 autoindent cindent expandtab filetype=css: */
/*! 
 * Fancy styles for pdf2htmlEX
 * Copyright 2012,2013 Lu Wang <<EMAIL>> 
 * https://github.com/coolwanglu/pdf2htmlEX/blob/master/share/LICENSE
 */
@keyframes fadein { from { opacity:0;} to { opacity:1;} }
@-webkit-keyframes fadein { from { opacity:0;} to { opacity:1;} }
@keyframes swing {
  0%  { transform: rotate(0deg); }
  10% { transform: rotate(0deg); }
  90% { transform: rotate(720deg); }
  100%{ transform: rotate(720deg); }
}
@-webkit-keyframes swing {
  0%  { -webkit-transform: rotate(0deg); }
  10% { -webkit-transform: rotate(0deg); }
  90% { -webkit-transform: rotate(720deg); }
  100%{ -webkit-transform: rotate(720deg); }
}
@media screen { 
  #sidebar {
    background-color:#2f3236;
    /* modified from http://philbit.com/svgpatterns/#crossstripes */
    background-image:url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0IiBoZWlnaHQ9IjQiPgo8cmVjdCB3aWR0aD0iNCIgaGVpZ2h0PSI0IiBmaWxsPSIjNDAzYzNmIj48L3JlY3Q+CjxwYXRoIGQ9Ik0wIDBMNCA0Wk00IDBMMCA0WiIgc3Ryb2tlLXdpZHRoPSIxIiBzdHJva2U9IiMxZTI5MmQiPjwvcGF0aD4KPC9zdmc+");
  }
  #outline {
    font-family:Georgia,Times,"Times New Roman",serif;
    font-size:13px;
    margin:2em 1em;
  }
  #outline ul {
    padding:0;
  }
  #outline li {
    list-style-type:none;
    margin:1em 0;
  }
  #outline li > ul {
    margin-left: 1em;
  }
  #outline a,
  #outline a:visited,
  #outline a:hover,
  #outline a:active {
    line-height:1.2;
    color:#e8e8e8;
    text-overflow:ellipsis;
    white-space:nowrap;
    text-decoration:none;
    display:block;
    overflow:hidden;
    outline:0;
  }
  #outline a:hover {
    color:rgb(0,204,255);
  }
  #page-container {
    background-color:#9e9e9e;
    /* http://philbit.com/svgpatterns/#thinstripes */
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjOWU5ZTllIj48L3JlY3Q+CjxwYXRoIGQ9Ik0wIDVMNSAwWk02IDRMNCA2Wk0tMSAxTDEgLTFaIiBzdHJva2U9IiM4ODgiIHN0cm9rZS13aWR0aD0iMSI+PC9wYXRoPgo8L3N2Zz4=");
    -webkit-transition:left 500ms;
    transition:left 500ms;
  }
  .pf {
    margin: 13px auto;
    box-shadow: 1px 1px 3px 1px #333;
    /* Needed by IE to make box-shadow works * https://developer.mozilla.org/en-US/docs/Web/CSS/box-shadow */
    border-collapse: separate;
  }
  .pc.opened { /* used by pdf2htmlEX.js, to show/hide pages */
    -webkit-animation: fadein 100ms;
    animation: fadein 100ms; 
  }
  .loading-indicator.active {
    /* 
     * use 0.01s instead of 0s,
     * since YUI Compressor will change 0s to 0,
     * which is not recognized by Firefox
     */
    -webkit-animation: swing 1.5s ease-in-out 0.01s infinite alternate none;
    animation: swing 1.5s ease-in-out 0.01s infinite alternate none;
  }
  .checked {
    background: no-repeat url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAWCAYAAADEtGw7AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3goQDSYgDiGofgAAAslJREFUOMvtlM9LFGEYx7/vvOPM6ywuuyPFihWFBUsdNnA6KLIh+QPx4KWExULdHQ/9A9EfUodYmATDYg/iRewQzklFWxcEBcGgEplDkDtI6sw4PzrIbrOuedBb9MALD7zv+3m+z4/3Bf7bZS2bzQIAcrmcMDExcTeXy10DAFVVAQDksgFUVZ1ljD3yfd+0LOuFpmnvVVW9GHhkZAQcxwkNDQ2FSCQyRMgJxnVdy7KstKZpn7nwha6urqqfTqfPBAJAuVymlNLXoigOhfd5nmeiKL5TVTV+lmIKwAOA7u5u6Lped2BsbOwjY6yf4zgQQkAIAcedaPR9H67r3uYBQFEUFItFtLe332lpaVkUBOHK3t5eRtf1DwAwODiIubk5DA8PM8bYW1EU+wEgCIJqsCAIQAiB7/u253k2BQDDMJBKpa4mEon5eDx+UxAESJL0uK2t7XosFlvSdf0QAEmlUnlRFJ9Waho2Qghc1/U9z3uWz+eX+Wr+lL6SZfleEAQIggA8z6OpqSknimIvYyybSCReMsZ6TislhCAIAti2Dc/zejVNWwCAavN8339j27YbTg0AGGM3WltbP4WhlRWq6Q/btrs1TVsYHx+vNgqKoqBUKn2NRqPFxsbGJzzP05puUlpt0ukyOI6z7zjOwNTU1OLo6CgmJyf/gA3DgKIoWF1d/cIY24/FYgOU0pp0z/Ityzo8Pj5OTk9PbwHA+vp6zWghDC+VSiuRSOQgGo32UErJ38CO42wdHR09LBQK3zKZDDY2NupmFmF4R0cHVlZWlmRZ/iVJUn9FeWWcCCE4ODjYtG27Z2Zm5juAOmgdGAB2d3cBADs7O8uSJN2SZfl+WKlpmpumaT6Yn58vn/fs6XmbhmHMNjc3tzDGFI7jYJrm5vb29sDa2trPC/9aiqJUy5pOp4f6+vqeJ5PJBAB0dnZe/t8NBajx/z37Df5OGX8d13xzAAAAAElFTkSuQmCC);
  }
}
/* Fancy CSS END */
</style>
<style type="text/css">
.ff0{font-family:sans-serif;visibility:hidden;}
@font-face{font-family:ff1;src:url('data:application/font-woff;base64,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')format("woff");}.ff1{font-family:ff1;line-height:0.928223;font-style:normal;font-weight:normal;visibility:visible;}
.m0{transform:matrix(0.250000,0.000000,0.000000,0.250000,0,0);-ms-transform:matrix(0.250000,0.000000,0.000000,0.250000,0,0);-webkit-transform:matrix(0.250000,0.000000,0.000000,0.250000,0,0);}
.v0{vertical-align:0.000000px;}
.ls1{letter-spacing:0.000000px;}
.ls0{letter-spacing:0.009399px;}
.sc_{text-shadow:none;}
.sc0{text-shadow:-0.015em 0 transparent,0 0.015em transparent,0.015em 0 transparent,0 -0.015em  transparent;}
@media screen and (-webkit-min-device-pixel-ratio:0){
.sc_{-webkit-text-stroke:0px transparent;}
.sc0{-webkit-text-stroke:0.015em transparent;text-shadow:none;}
}
.ws0{word-spacing:-13.074236px;}
.ws1{word-spacing:0.000000px;}
.fc0{color:rgb(0,0,0);}
.fs0{font-size:46.995816px;}
.y5{bottom:2.937274px;}
.y4{bottom:16.644386px;}
.y3{bottom:44.058613px;}
.y2{bottom:57.765726px;}
.y1{bottom:651.464400px;}
.y0{bottom:651.500000px;}
.h3{height:33.824137px;}
.h1{height:68.500000px;}
.h2{height:68.535560px;}
.h0{height:792.000000px;}
.w1{width:468.000000px;}
.w0{width:612.000000px;}
.x1{left:4.895398px;}
.x0{left:72.000000px;}
@media print{
.v0{vertical-align:0.000000pt;}
.ls1{letter-spacing:0.000000pt;}
.ls0{letter-spacing:0.012532pt;}
.ws0{word-spacing:-17.432315pt;}
.ws1{word-spacing:0.000000pt;}
.fs0{font-size:62.661088pt;}
.y5{bottom:3.916365pt;}
.y4{bottom:22.192515pt;}
.y3{bottom:58.744817pt;}
.y2{bottom:77.020967pt;}
.y1{bottom:868.619200pt;}
.y0{bottom:868.666667pt;}
.h3{height:45.098849pt;}
.h1{height:91.333333pt;}
.h2{height:91.380747pt;}
.h0{height:1056.000000pt;}
.w1{width:624.000000pt;}
.w0{width:816.000000pt;}
.x1{left:6.527197pt;}
.x0{left:96.000000pt;}
}
</style>
<script>
/*
 Copyright 2012 Mozilla Foundation 
 Copyright 2013 Lu Wang <<EMAIL>>
 Apachine License Version 2.0 
*/
(function(){function b(a,b,e,f){var c=(a.className||"").split(/\s+/g);""===c[0]&&c.shift();var d=c.indexOf(b);0>d&&e&&c.push(b);0<=d&&f&&c.splice(d,1);a.className=c.join(" ");return 0<=d}if(!("classList"in document.createElement("div"))){var e={add:function(a){b(this.element,a,!0,!1)},contains:function(a){return b(this.element,a,!1,!1)},remove:function(a){b(this.element,a,!1,!0)},toggle:function(a){b(this.element,a,!0,!0)}};Object.defineProperty(HTMLElement.prototype,"classList",{get:function(){if(this._classList)return this._classList;
var a=Object.create(e,{element:{value:this,writable:!1,enumerable:!0}});Object.defineProperty(this,"_classList",{value:a,writable:!1,enumerable:!1});return a},enumerable:!0})}})();
</script>
<script>
/* vim: set shiftwidth=2 tabstop=2 autoindent cindent expandtab filetype=javascript : */
/** 
 * @license pdf2htmlEX.js: Core UI functions for pdf2htmlEX 
 * Copyright 2012,2013 Lu Wang <<EMAIL>> and other contributors 
 * https://github.com/coolwanglu/pdf2htmlEX/blob/master/share/LICENSE 
 */

/*
 * Attention:
 * This files is to be optimized by closure-compiler, 
 * so pay attention to the forms of property names:
 *
 * string/bracket form is safe, won't be optimized:
 * var obj={ 'a':'b' }; obj['a'] = 'b';
 * name/dot form will be optimized, the name is likely to be modified:
 * var obj={ a:'b' }; obj.a = 'b';
 *
 * Either form can be used for internal objects, 
 * but must be consistent for each one respectively.
 *
 * string/bracket form must be used for external objects
 * e.g. DEFAULT_CONFIG, object stored in page-data
 * property names are part of the `protocol` in these cases.
 *
 */

'use strict';

var pdf2htmlEX = window['pdf2htmlEX'] = window['pdf2htmlEX'] || {};

/** 
 * @const 
 * @struct
 */
var CSS_CLASS_NAMES = {
  page_frame       : 'pf',
  page_content_box : 'pc',
  page_data        : 'pi',
  background_image : 'bi',
  link             : 'l',
  input_radio      : 'ir',
  __dummy__        : 'no comma'
};

/** 
 * configurations of Viewer
 * @const 
 * @dict
 */
var DEFAULT_CONFIG = {
  // id of the element to put the pages in
  'container_id' : 'page-container',
  // id of the element for sidebar (to open and close)
  'sidebar_id' : 'sidebar',
  // id of the element for outline
  'outline_id' : 'outline',
  // class for the loading indicator
  'loading_indicator_cls' : 'loading-indicator',
  // How many page shall we preload that are below the last visible page
  'preload_pages' : 3,
  // how many ms should we wait before actually rendering the pages and after a scroll event
  'render_timeout' : 100,
  // zoom ratio step for each zoom in/out event
  'scale_step' : 0.9,
  // register global key handler, allowing navigation by keyboard
  'key_handler' : true,
  // register hashchange handler, navigate to the location specified by the hash
  'hashchange_handler' : true,
  // register view history handler, allowing going back to the previous location
  'view_history_handler' : true,

  '__dummy__'        : 'no comma'
};

/** @const */
var EPS = 1e-6;

/************************************/
/* utility function */
/**
 * @param{Array.<number>} ctm
 */
function invert(ctm) {
  var det = ctm[0] * ctm[3] - ctm[1] * ctm[2];
  return [ ctm[3] / det
          ,-ctm[1] / det
          ,-ctm[2] / det
          ,ctm[0] / det
          ,(ctm[2] * ctm[5] - ctm[3] * ctm[4]) / det
          ,(ctm[1] * ctm[4] - ctm[0] * ctm[5]) / det
        ];
};
/**
 * @param{Array.<number>} ctm
 * @param{Array.<number>} pos
 */
function transform(ctm, pos) {
  return [ctm[0] * pos[0] + ctm[2] * pos[1] + ctm[4]
         ,ctm[1] * pos[0] + ctm[3] * pos[1] + ctm[5]];
};

/**
 * @param{Element} ele
 */
function get_page_number(ele) {
  return parseInt(ele.getAttribute('data-page-no'), 16);
};

/**
 * @param{NodeList} eles
 */
function disable_dragstart(eles) {
  for (var i = 0, l = eles.length; i < l; ++i) {
    eles[i].addEventListener('dragstart', function() {
      return false;
    }, false);
  }
};

/**
 * @param{...Object} var_args
 */
function clone_and_extend_objs(var_args) {
  var result_obj = {};
  for (var i = 0, l = arguments.length; i < l; ++i) {
    var cur_obj = arguments[i];
    for (var k in cur_obj) {
      if (cur_obj.hasOwnProperty(k)) {
        result_obj[k] = cur_obj[k];
      }
    }
  }
  return result_obj;
};

/** 
 * @constructor 
 * @param{Element} page The element for the page
 */
function Page(page) {
  if (!page) return;

  this.loaded = false;
  this.shown = false;
  this.page = page; // page frame element

  this.num = get_page_number(page);

  // page size
  // Need to make rescale work when page_content_box is not loaded, yet
  this.original_height = page.clientHeight;     
  this.original_width = page.clientWidth;

  // content box
  var content_box = page.getElementsByClassName(CSS_CLASS_NAMES.page_content_box)[0];

  // if page is loaded
  if (content_box) {
    this.content_box = content_box;
    /*
     * scale ratios
     *
     * original_scale : the first one
     * cur_scale : currently using
     */
    this.original_scale = this.cur_scale = this.original_height / content_box.clientHeight;
    this.page_data = JSON.parse(page.getElementsByClassName(CSS_CLASS_NAMES.page_data)[0].getAttribute('data-data'));

    this.ctm = this.page_data['ctm'];
    this.ictm = invert(this.ctm);

    this.loaded = true;
  }
};
Page.prototype = {
  /* hide & show are for contents, the page frame is still there */
  hide : function(){
    if (this.loaded && this.shown) {
      this.content_box.classList.remove('opened');
      this.shown = false;
    }
  },
  show : function(){
    if (this.loaded && !this.shown) {
      this.content_box.classList.add('opened');
      this.shown = true;
    }
  },
  /**
   * @param{number} ratio
   */
  rescale : function(ratio) {
    if (ratio === 0) {
      // reset scale
      this.cur_scale = this.original_scale;
    } else {
      this.cur_scale = ratio;
    }

    // scale the content box
    if (this.loaded) {
      var cbs = this.content_box.style;
      cbs.msTransform = cbs.webkitTransform = cbs.transform = 'scale('+this.cur_scale.toFixed(3)+')';
    }

    // stretch the page frame to hold the place
    {
      var ps = this.page.style;
      ps.height = (this.original_height * this.cur_scale) + 'px';
      ps.width = (this.original_width * this.cur_scale) + 'px';
    }
  },
  /*
   * return the coordinate of the top-left corner of container
   * in our coordinate system
   * assuming that p.parentNode === p.offsetParent
   */
  view_position : function () {
    var p = this.page;
    var c = p.parentNode;
    return [c.scrollLeft - p.offsetLeft - p.clientLeft
           ,c.scrollTop - p.offsetTop - p.clientTop];
  },
  height : function () {
    return this.page.clientHeight;
  },
  width : function () {
    return this.page.clientWidth;
  }
};

/** 
 * @constructor
 * @param{Object=} config
 */
function Viewer(config) {
  this.config = clone_and_extend_objs(DEFAULT_CONFIG, (arguments.length > 0 ? config : {}));
  this.pages_loading = [];
  this.init_before_loading_content();

  var self = this;
  document.addEventListener('DOMContentLoaded', function(){
    self.init_after_loading_content();
  }, false);
};

Viewer.prototype = {
  scale : 1,
  /* 
   * index of the active page (the one with largest visible area)
   * which estimates the page currently being viewed
   */
  cur_page_idx : 0,

  /*
   * index of the first visible page
   * used when determining current view
   */
  first_page_idx : 0,

  init_before_loading_content : function() {
    /* hide all pages before loading, will reveal only visible ones later */
    this.pre_hide_pages();
  },

  initialize_radio_button : function() {
    var elements = document.getElementsByClassName(CSS_CLASS_NAMES.input_radio);
    
    for(var i = 0; i < elements.length; i++) {
      var r = elements[i];

      r.addEventListener('click', function() {
        this.classList.toggle("checked");
      });
    }
  },

  init_after_loading_content : function() {
    this.sidebar = document.getElementById(this.config['sidebar_id']);
    this.outline = document.getElementById(this.config['outline_id']);
    this.container = document.getElementById(this.config['container_id']);
    this.loading_indicator = document.getElementsByClassName(this.config['loading_indicator_cls'])[0];

    
    {
      // Open the outline if nonempty
      var empty = true;
      var nodes = this.outline.childNodes;
      for (var i = 0, l = nodes.length; i < l; ++i) {
        var cur_node = nodes[i];
        if (cur_node.nodeName.toLowerCase() === 'ul') {
          empty = false;
          break;
        }
      }
      if (!empty)
        this.sidebar.classList.add('opened');
    }

    this.find_pages();
    // do nothing if there's nothing
    if(this.pages.length == 0) return;

    // disable dragging of background images
    disable_dragstart(document.getElementsByClassName(CSS_CLASS_NAMES.background_image));

    if (this.config['key_handler'])
      this.register_key_handler();

    var self = this;

    if (this.config['hashchange_handler']) {
      window.addEventListener('hashchange', function(e) {
        self.navigate_to_dest(document.location.hash.substring(1));
      }, false);
    }

    if (this.config['view_history_handler']) {
      window.addEventListener('popstate', function(e) {
        if(e.state) self.navigate_to_dest(e.state);
      }, false);
    }

    // register schedule rendering
    // renew old schedules since scroll() may be called frequently
    this.container.addEventListener('scroll', function() {
      self.update_page_idx();
      self.schedule_render(true);
    }, false);

    // handle links
    [this.container, this.outline].forEach(function(ele) {
      ele.addEventListener('click', self.link_handler.bind(self), false);
    });

    this.initialize_radio_button();
    this.render();
  },

  /*
   * set up this.pages and this.page_map
   * pages is an array holding all the Page objects
   * page-Map maps an original page number (in PDF) to the corresponding index in page
   */
  find_pages : function() {
    var new_pages = [];
    var new_page_map = {};
    var nodes = this.container.childNodes;
    for (var i = 0, l = nodes.length; i < l; ++i) {
      var cur_node = nodes[i];
      if ((cur_node.nodeType === Node.ELEMENT_NODE)
          && cur_node.classList.contains(CSS_CLASS_NAMES.page_frame)) {
        var p = new Page(cur_node);
        new_pages.push(p);
        new_page_map[p.num] = new_pages.length - 1;
      }
    }
    this.pages = new_pages;
    this.page_map = new_page_map;
  },

  /**
   * @param{number} idx
   * @param{number=} pages_to_preload
   * @param{function(Page)=} callback
   *
   * TODO: remove callback -> promise ?
   */
  load_page : function(idx, pages_to_preload, callback) {
    var pages = this.pages;
    if (idx >= pages.length)
      return;  // Page does not exist

    var cur_page = pages[idx];
    if (cur_page.loaded)
      return;  // Page is loaded

    if (this.pages_loading[idx])
      return;  // Page is already loading

    var cur_page_ele = cur_page.page;
    var url = cur_page_ele.getAttribute('data-page-url');
    if (url) {
      this.pages_loading[idx] = true;       // set semaphore

      // add a copy of the loading indicator if not already present
      var new_loading_indicator = cur_page_ele.getElementsByClassName(this.config['loading_indicator_cls'])[0];
      if (typeof new_loading_indicator === 'undefined'){
        new_loading_indicator = this.loading_indicator.cloneNode(true);
        new_loading_indicator.classList.add('active');
        cur_page_ele.appendChild(new_loading_indicator);
      }

      // load data
      {
        var self = this;
        var _idx = idx;
        var xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);
        xhr.onload = function(){
          if (xhr.status === 200 || xhr.status === 0) {
            // find the page element in the data
            var div = document.createElement('div');
            div.innerHTML = xhr.responseText;

            var new_page = null;
            var nodes = div.childNodes;
            for (var i = 0, l = nodes.length; i < l; ++i) {
              var cur_node = nodes[i];
              if ((cur_node.nodeType === Node.ELEMENT_NODE)
                  && cur_node.classList.contains(CSS_CLASS_NAMES.page_frame)) {
                new_page = cur_node;
                break;
              }
            }

            // replace the old page with loaded data
            // the loading indicator on this page should also be destroyed
            var p = self.pages[_idx];
            self.container.replaceChild(new_page, p.page);
            p = new Page(new_page);
            self.pages[_idx] = p;

            p.hide();
            p.rescale(self.scale);

            // disable background image dragging
            disable_dragstart(new_page.getElementsByClassName(CSS_CLASS_NAMES.background_image));

            self.schedule_render(false);

            if (callback){ callback(p); }
          }

          // Reset loading token
          delete self.pages_loading[_idx];
        };
        xhr.send(null);
      }
    }
    // Concurrent prefetch of the next pages
    if (pages_to_preload === undefined)
      pages_to_preload = this.config['preload_pages'];

    if (--pages_to_preload > 0) {
      var self = this;
      setTimeout(function() {
        self.load_page(idx+1, pages_to_preload);
      },0);
    }
  },

  /*
   * Hide all pages that have no 'opened' class
   * The 'opened' class will be added to visible pages by JavaScript
   * We cannot add this in the default CSS because JavaScript may be disabled
   */
  pre_hide_pages : function() {
    /* pages might have not been loaded yet, so add a CSS rule */
    var s = '@media screen{.'+CSS_CLASS_NAMES.page_content_box+'{display:none;}}';
    var n = document.createElement('style');
    if (n.styleSheet) {
      n.styleSheet.cssText = s;
    } else {
      n.appendChild(document.createTextNode(s));
    }
    document.head.appendChild(n);
  },

  /*
   * show visible pages and hide invisible pages
   */
  render : function () {
    var container = this.container;
    /* 
     * show the pages that are 'nearly' visible -- it's right above or below the container
     *
     * all the y values are in the all-page element's coordinate system
     */
    var container_min_y = container.scrollTop;
    var container_height = container.clientHeight;
    var container_max_y = container_min_y + container_height;
    var visible_min_y = container_min_y - container_height;
    var visible_max_y = container_max_y + container_height;

    var cur_page_fully_visible = false;
    var cur_page_idx = this.cur_page_idx;
    var max_visible_page_idx = cur_page_idx;
    var max_visible_ratio = 0.0;

    var pl = this.pages;
    for (var i = 0, l = pl.length; i < l; ++i) {
      var cur_page = pl[i];
      var cur_page_ele = cur_page.page;
      var page_min_y = cur_page_ele.offsetTop + cur_page_ele.clientTop;
      var page_height = cur_page_ele.clientHeight;
      var page_max_y = page_min_y + page_height;
      if ((page_min_y <= visible_max_y) && (page_max_y >= visible_min_y))
      {
        // cur_page is 'nearly' visible, show it or load it
        if (cur_page.loaded) {
          cur_page.show();
        } else {
          this.load_page(i);
        }
      } else {
        cur_page.hide();
      }
    }
  },
  /*
   * update cur_page_idx and first_page_idx
   * normally called upon scrolling
   */
  update_page_idx: function () {
    var pages = this.pages;
    var pages_len = pages.length;
    // there is no chance that cur_page_idx or first_page_idx is modified
    if (pages_len < 2) return;
   
    var container = this.container;
    var container_min_y = container.scrollTop;
    var container_max_y = container_min_y + container.clientHeight;

    // binary search for the first page
    // whose bottom border is below the top border of the container
    var first_idx = -1;
    var last_idx = pages_len;
    var rest_len = last_idx - first_idx;
    // TODO: use current first_page_idx as a hint?
    while(rest_len > 1) {
      var idx = first_idx + Math.floor(rest_len / 2);
      var cur_page_ele = pages[idx].page;
      if (cur_page_ele.offsetTop + cur_page_ele.clientTop + cur_page_ele.clientHeight >= container_min_y) {
        last_idx = idx;
      } else {
        first_idx = idx;
      }
      rest_len = last_idx - first_idx;
    }
    
    /*
     * with malformed settings it is possible that no page is visible, e.g.
     * - the container is to thin, which lies in the margin between two pages
     * - all pages are completely above or below the container
     * but we just assume that they won't happen.
     */
    this.first_page_idx = last_idx;

    // find the page with largest visible area
    var cur_page_idx = this.cur_page_idx;
    var max_visible_page_idx = cur_page_idx;
    var max_visible_ratio = 0.0;

    for(var i = last_idx; i < pages_len; ++i) {
      var cur_page_ele = pages[i].page;
      var page_min_y = cur_page_ele.offsetTop + cur_page_ele.clientTop;
      var page_height = cur_page_ele.clientHeight;
      var page_max_y = page_min_y + page_height;
      if (page_min_y > container_max_y) break;

      // check the visible fraction of the page
      var page_visible_ratio = ( Math.min(container_max_y, page_max_y) 
                                 - Math.max(container_min_y, page_min_y)
                               ) / page_height;

      // stay with the current page if it is still fully visible
      if ((i === cur_page_idx) && (Math.abs(page_visible_ratio - 1.0) <= EPS)) {
        max_visible_page_idx = cur_page_idx;
        break;
      }

      if (page_visible_ratio > max_visible_ratio) {
        max_visible_ratio = page_visible_ratio;
        max_visible_page_idx = i;
      }
    }

    this.cur_page_idx = max_visible_page_idx;
  },

  /**
   * @param{boolean} renew renew the existing schedule instead of using the old one
   */
  schedule_render : function(renew) {
    if (this.render_timer !== undefined) {
      if (!renew) return;
      clearTimeout(this.render_timer);
    }

    var self = this;
    this.render_timer = setTimeout(function () {
      /*
       * render() may trigger load_page(), which may in turn trigger another render()
       * so delete render_timer first
       */
      delete self.render_timer;
      self.render();
    }, this.config['render_timeout']);
  },

  /*
   * Handling key events, zooming, scrolling etc.
   */
  register_key_handler: function () {
    /* 
     * When user try to zoom in/out using ctrl + +/- or mouse wheel
     * handle this and prevent the default behaviours
     *
     * Code credit to PDF.js
     */
    var self = this;

    // Firefox specific event, so that we can prevent browser from zooming
    window.addEventListener('DOMMouseScroll', function(e) {
      if (e.ctrlKey) {
        e.preventDefault();
        var container = self.container;
        var rect = container.getBoundingClientRect();
        var fixed_point = [e.clientX - rect['left'] - container.clientLeft
                          ,e.clientY - rect['top'] - container.clientTop];
        self.rescale(Math.pow(self.config['scale_step'], e.detail), true, fixed_point);
      }
    }, false);

    window.addEventListener('keydown', function(e) {
      var handled = false;
      /*
      var cmd = (e.ctrlKey ? 1 : 0)
                | (e.altKey ? 2 : 0)
                | (e.shiftKey ? 4 : 0)
                | (e.metaKey ? 8 : 0)
                ;
                */
      var with_ctrl = e.ctrlKey || e.metaKey;
      var with_alt = e.altKey;
      switch (e.keyCode) {
        case 61: // FF/Mac '='
        case 107: // FF '+' and '='
        case 187: // Chrome '+'
          if (with_ctrl){
            self.rescale(1.0 / self.config['scale_step'], true);
            handled = true;
          }
          break;
        case 173: // FF/Mac '-'
        case 109: // FF '-'
        case 189: // Chrome '-'
          if (with_ctrl){
            self.rescale(self.config['scale_step'], true);
            handled = true;
          }
          break;
        case 48: // '0'
          if (with_ctrl){
            self.rescale(0, false);
            handled = true;
          }
          break;
        case 33: // Page UP:
          if (with_alt) { // alt-pageup    -> scroll one page up
            self.scroll_to(self.cur_page_idx - 1);
          } else { // pageup        -> scroll one screen up
            self.container.scrollTop -= self.container.clientHeight;
          }
          handled = true;
          break;
        case 34: // Page DOWN
          if (with_alt) { // alt-pagedown  -> scroll one page down
            self.scroll_to(self.cur_page_idx + 1);
          } else { // pagedown      -> scroll one screen down
            self.container.scrollTop += self.container.clientHeight;
          }
          handled = true;
          break;
        case 35: // End
          self.container.scrollTop = self.container.scrollHeight;
          handled = true;
          break;
        case 36: // Home
          self.container.scrollTop = 0;
          handled = true;
          break;
      }
      if (handled) {
        e.preventDefault();
        return;
      }
    }, false);
  },

  /**
   * @param{number} ratio
   * @param{boolean} is_relative
   * @param{Array.<number>=} fixed_point preserve the position (relative to the top-left corner of the viewer) after rescaling
   */
  rescale : function (ratio, is_relative, fixed_point) {
    var old_scale = this.scale;
    var new_scale = old_scale;
    // set new scale
    if (ratio === 0) {
      new_scale = 1;
      is_relative = false;
    } else if (is_relative)
      new_scale *= ratio;
    else
      new_scale = ratio;

    this.scale = new_scale;

    if (!fixed_point)
      fixed_point = [0,0];

    // translate fixed_point to the coordinate system of all pages
    var container = this.container;
    fixed_point[0] += container.scrollLeft;
    fixed_point[1] += container.scrollTop;

    // find the visible page that contains the fixed point
    // if the fixed point lies between two pages (including their borders), it's contained in the first one
    var pl = this.pages;
    var pl_len = pl.length;
    for (var i = this.first_page_idx; i < pl_len; ++i) {
      var p = pl[i].page;
      if (p.offsetTop + p.clientTop >= fixed_point[1])
        break;
    }
    var fixed_point_page_idx = i - 1;

    // determine the new scroll position
    // each-value consists of two parts, one inside the page, which is affected by rescaling,
    // the other is outside, (e.g. borders and margins), which is not affected

    // if the fixed_point is above the first page, use the first page as the reference
    if (fixed_point_page_idx < 0) 
      fixed_point_page_idx = 0;

    var fp_p = pl[fixed_point_page_idx].page;
    var fp_p_width = fp_p.clientWidth;
    var fp_p_height = fp_p.clientHeight;

    var fp_x_ref = fp_p.offsetLeft + fp_p.clientLeft;
    var fp_x_inside = fixed_point[0] - fp_x_ref;
    if (fp_x_inside < 0)
      fp_x_inside = 0;
    else if (fp_x_inside > fp_p_width)
      fp_x_inside = fp_p_width;

    var fp_y_ref = fp_p.offsetTop + fp_p.clientTop;
    var fp_y_inside = fixed_point[1] - fp_y_ref;
    if (fp_y_inside < 0)
      fp_y_inside = 0;
    else if (fp_y_inside > fp_p_height)
      fp_y_inside = fp_p_height;

    // Rescale pages
    for (var i = 0; i < pl_len; ++i) 
        pl[i].rescale(new_scale);  

    // Correct container scroll to keep view aligned while zooming
    container.scrollLeft += fp_x_inside / old_scale * new_scale + fp_p.offsetLeft + fp_p.clientLeft - fp_x_inside - fp_x_ref;
    container.scrollTop += fp_y_inside / old_scale * new_scale + fp_p.offsetTop + fp_p.clientTop - fp_y_inside - fp_y_ref;

    // some pages' visibility may be toggled, wait for next render()
    // renew old schedules since rescale() may be called frequently
    this.schedule_render(true);
  },

  fit_width : function () {
    var page_idx = this.cur_page_idx;
    this.rescale(this.container.clientWidth / this.pages[page_idx].width(), true);
    this.scroll_to(page_idx);
  },

  fit_height : function () {
    var page_idx = this.cur_page_idx;
    this.rescale(this.container.clientHeight / this.pages[page_idx].height(), true);
    this.scroll_to(page_idx);
  },
  /**
   * @param{Node} ele
   */
  get_containing_page : function(ele) {
    /* get the page obj containing obj */
    while(ele) {
      if ((ele.nodeType === Node.ELEMENT_NODE)
          && ele.classList.contains(CSS_CLASS_NAMES.page_frame)) {
        /*
         * Get original page number and map it to index of pages
         * TODO: store the index on the dom element
         */
        var pn = get_page_number(/** @type{Element} */(ele));
        var pm = this.page_map;
        return (pn in pm) ? this.pages[pm[pn]] : null;
      }
      ele = ele.parentNode;
    }
    return null;
  },

  /**
   * @param{Event} e
   */
  link_handler : function (e) {
    var target = /** @type{Node} */(e.target);
    var detail_str = /** @type{string} */ (target.getAttribute('data-dest-detail'));
    if (!detail_str) return;

    if (this.config['view_history_handler']) {
      try {
        var cur_hash = this.get_current_view_hash();
        window.history.replaceState(cur_hash, '', '#' + cur_hash);
        window.history.pushState(detail_str, '', '#' + detail_str);
      } catch(ex) { }
    }
    this.navigate_to_dest(detail_str, this.get_containing_page(target));
    e.preventDefault();
  },

  /**
   * @param{string} detail_str may come from user provided hashtag, need sanitzing
   * @param{Page=} src_page page containing the source event (e.g. link)
   */
  navigate_to_dest : function(detail_str, src_page) {
    try {
      var detail = JSON.parse(detail_str);
    } catch(e) {
      return;
    }

    if(!(detail instanceof Array)) return;

    var target_page_no = detail[0];
    var page_map = this.page_map;
    if (!(target_page_no in page_map)) return;
    var target_page_idx = page_map[target_page_no];
    var target_page = this.pages[target_page_idx];

    for (var i = 2, l = detail.length; i < l; ++i) {
      var d = detail[i];
      if(!((d === null) || (typeof d === 'number')))
        return;
    }

    while(detail.length < 6)
      detail.push(null);

    // cur_page might be undefined, e.g. from Outline
    var cur_page = src_page || this.pages[this.cur_page_idx];

    var cur_pos = cur_page.view_position();
    cur_pos = transform(cur_page.ictm, [cur_pos[0], cur_page.height()-cur_pos[1]]);

    var zoom = this.scale;
    var pos = [0,0];
    var upside_down = true;
    var ok = false;

    // position specified in `detail` are in the raw coordinate system of the page (unscaled)
    var scale = this.scale;
    // TODO: fitb*
    // TODO: BBox
    switch(detail[1]) {
      case 'XYZ':
        pos = [ (detail[2] === null) ? cur_pos[0] : detail[2] * scale
              , (detail[3] === null) ? cur_pos[1] : detail[3] * scale ];
        zoom = detail[4];
        if ((zoom === null) || (zoom === 0))
          zoom = this.scale;
        ok = true;
        break;
      case 'Fit':
      case 'FitB':
        pos = [0,0];
        ok = true;
        break;
      case 'FitH':
      case 'FitBH':
        pos = [0, (detail[2] === null) ? cur_pos[1] : detail[2] * scale];
        ok = true;
        break;
      case 'FitV':
      case 'FitBV':
        pos = [(detail[2] === null) ? cur_pos[0] : detail[2] * scale, 0];
        ok = true;
        break;
      case 'FitR':
        /* locate the top-left corner of the rectangle */
        // TODO
        pos = [detail[2] * scale, detail[5] * scale];
        upside_down = false;
        ok = true;
        break;
      default:
        break;
    }

    if (!ok) return;

    this.rescale(zoom, false);

    var self = this;
    /**
     * page should of type Page 
     * @param{Page} page 
     */
    var transform_and_scroll = function(page) {
      pos = transform(page.ctm, pos);
      if (upside_down) {
        pos[1] = page.height() - pos[1];
      }
      self.scroll_to(target_page_idx, pos);
    };

    if (target_page.loaded) {
      transform_and_scroll(target_page);
    } else {
      // TODO: scroll_to may finish before load_page

      // Scroll to the exact position once loaded.
      this.load_page(target_page_idx, undefined, transform_and_scroll);

      // In the meantime page gets loaded, scroll approximately position for maximum responsiveness.
      this.scroll_to(target_page_idx);
    }
  }, 

  /**
   * @param{number} page_idx
   * @param{Array.<number>=} pos [x,y] where (0,0) is the top-left corner
   */
  scroll_to : function(page_idx, pos) {
    var pl = this.pages;
    if ((page_idx < 0) || (page_idx >= pl.length)) return;
    var target_page = pl[page_idx];
    var cur_target_pos = target_page.view_position();

    if (pos === undefined)
      pos = [0,0];

    var container = this.container;
    container.scrollLeft += pos[0] - cur_target_pos[0];
    container.scrollTop += pos[1] - cur_target_pos[1];
  },

  /**
   * generate the hash for the current view
   */
  get_current_view_hash : function() {
    var detail = [];
    var cur_page = this.pages[this.cur_page_idx];

    detail.push(cur_page.num);
    detail.push('XYZ');

    var cur_pos = cur_page.view_position();
    cur_pos = transform(cur_page.ictm, [cur_pos[0], cur_page.height()-cur_pos[1]]);
    detail.push(cur_pos[0] / this.scale);
    detail.push(cur_pos[1] / this.scale);
    
    detail.push(this.scale);

    return JSON.stringify(detail);
  }
};

// export pdf2htmlEX.Viewer
pdf2htmlEX['Viewer'] = Viewer;
</script>
<script>
try{
pdf2htmlEX.defaultViewer = new pdf2htmlEX.Viewer({});
}catch(e){}
</script>
<title></title>
</head>
<body>
<div id="sidebar">
<div id="outline">
</div>
</div>
<div id="page-container">
<div id="pf1" class="pf w0 h0" data-page-no="1"><div class="pc pc1 w0 h0"><img class="bi x0 y0 w1 h1" alt="" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAA6gAAACJCAIAAACuO4JxAAAACXBIWXMAABYlAAAWJQFJUiTwAAACTUlEQVR42u3WQREAAAjDMMC/56EDLpHQVztJAQDAdyMBAADGFwAAjC8AABhfAAAwvgAAYHwBAMD4AgCA8QUAAOMLAIDxBQAA4wsAAMYXAACMLwAAGF8AADC+AABgfAEAwPgCAIDxBQDA+AIAgPEFAADjCwAAxhcAAIwvAAAYXwAAML4AAGB8AQDA+AIAYHwBAMD4AgCA8QUAAOMLAADGFwAAjC8AABhfAAAwvgAAYHwBADC+AABgfAEAwPgCAIDxBQAA4wsAAMYXAACMLwAAGF8AADC+AAAYXwAAML4AAGB8AQDA+AIAgPEFAADjCwAAxhcAAIwvAAAYXwAAjC8AABhfAAAwvgAAYHwBAMD4AgCA8QUAAOMLAADGFwAAjC8AAMYXAACMLwAAGF8AADC+AABgfAEAwPgCAIDxBQAA4wsAAMYXAADjCwAAxhcAAIwvAAAYXwAAML4AAGB8AQDA+AIAgPEFAADjCwCA8QUAAOMLAADGFwAAjC8AABhfAAAwvgAAYHwBAMD4AgBgfAEAwPgCAIDxBQAA4wsAAMYXAACMLwAAGF8AADC+AABgfAEAML4AAGB8AQDA+AIAgPEFAADjCwAAxhcAAIwvAAAYXwAAML4AABhfAAAwvgAAYHwBAMD4AgCA8QUAAOMLAADGFwAAjC8AABhfAACMLwAAGF8AADC+AABgfAEAwPgCAIDxBQAA4wsAAMYXAACMLwAAxhcAAIwvAAAYXwAAML4AAGB8AQDA+AIAgPEFAADjCwAAxhcAAOMLAADGFwAAjlqvBgQPTZweJgAAAABJRU5ErkJggg=="/><div class="c x0 y1 w1 h2"><div class="t m0 x1 h3 y2 ff1 fs0 fc0 sc0 ls0">Hello,</div><div class="t m0 x1 h3 y3 ff1 fs0 fc0 sc0 ls1 ws1">This is a test report</div><div class="t m0 x1 h3 y4 ff1 fs0 fc0 sc0 ls0 ws0">Love,</div><div class="t m0 x1 h3 y5 ff1 fs0 fc0 sc0 ls1">PH</div></div></div><div class="pi" data-data='{"ctm":[1.000000,0.000000,0.000000,1.000000,0.000000,0.000000]}'></div></div>
</div>
<div class="loading-indicator">
<img alt="" src="data:image/png;base64,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"/>
</div>
</body>
</html>
