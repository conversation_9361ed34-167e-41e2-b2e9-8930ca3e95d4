FROM ubuntu:trusty as reportprocessorbase

WORKDIR /reportprocessor

RUN apt-get update && apt-get install -y build-essential checkinstall git cmake

RUN apt-get install -y poppler-data autotools-dev libjpeg-dev libtiff4-dev libpng12-dev \
    libgif-dev libxt-dev autoconf automake libtool bzip2 libxml2-dev libuninameslist-dev \
    libspiro-dev python-dev libpango1.0-dev libcairo2-dev chrpath uuid-dev uthash-dev libopenjpeg-dev \
    sudo packaging-dev pkg-config python-dev g++ xz-utils wget

ARG POPPLER_VER=0.26.5
COPY poppler-$POPPLER_VER.tar.xz /tmp
RUN tar -C / -xf /tmp/poppler-$POPPLER_VER.tar.xz
RUN cd /poppler-$POPPLER_VER && ./configure --prefix=/usr --enable-xpdf-headers && make && make install
RUN rm /tmp/poppler-$POPPLER_VER.tar.xz

ADD freetype-2.6.3.tar.gz /
RUN cd / && cd freetype-2.6.3 && ./configure && make && make install

ADD fontconfig-2.12.0.tar.bz2 /
RUN cd / && cd fontconfig-2.12.0 && ./configure && make && make install

ADD fontforge.tar.gz /
RUN cd / && cd fontforge && ./autogen.sh && ./configure --prefix=/usr && make && make install && ldconfig

RUN cd / && git clone --depth=1 https://github.com/coolwanglu/pdf2htmlEX.git && \
    cd pdf2htmlEX && cmake . -DENABLE_SVG=ON && make && make install

RUN apt-get -qq update && apt-get -q -y install \
    xvfb \
    ghostscript \
    curl \
    imagemagick \
    libjpeg62 \
    && curl -o dcmtk-3.6.2-linux-x86_64.tar.bz2 -L -k https://dicom.offis.de/download/dcmtk/dcmtk362/bin/dcmtk-3.6.2-linux-x86_64.tar.bz2 \
    && tar xjf dcmtk-3.6.2-linux-x86_64.tar.bz2 \
    && curl -o libpng12-0_1.2.54-1ubuntu1.1_amd64.deb -L http://security.ubuntu.com/ubuntu/pool/main/libp/libpng/libpng12-0_1.2.54-1ubuntu1.1_amd64.deb \
    && dpkg -i libpng12-0_1.2.54-1ubuntu1.1_amd64.deb \
    && wget https://github.com/malaterre/GDCM/releases/download/v3.0.8/GDCM-3.0.8-Linux-x86_64.tar.bz2 \
    && tar xjf GDCM-3.0.8-Linux-x86_64.tar.bz2 \
    && cp GDCM-3.0.8-Linux-x86_64/* /usr/ -r

RUN wget https://github.com/wkhtmltopdf/wkhtmltopdf/releases/download/0.12.4/wkhtmltox-0.12.4_linux-generic-amd64.tar.xz && \
    tar xvf wkhtmltox-0.12.4_linux-generic-amd64.tar.xz && \
    mv wkhtmltox/bin/wkhtmltopdf /usr/bin && \
    mv wkhtmltox/bin/wkhtmltoimage /usr/bin && \
    rm wkhtmltox-0.12.4_linux-generic-amd64.tar.xz  && rm -rf wkhtmltox

#dependency for gdcmconv
RUN wget -O libstdc++6 http://security.ubuntu.com/ubuntu/pool/main/g/gcc-5/libstdc++6_5.4.0-6ubuntu1~16.04.12_amd64.deb
RUN dpkg --force-all -i libstdc++6

RUN sudo sed -i 's/rights="none" pattern="PDF"/rights="read | write" pattern="PDF"/' /etc/ImageMagick/policy.xml

ENV PATH="/reportprocessor/dcmtk-3.6.2-linux-x86_64/bin:${PATH}"
ENV DCMDICTPATH="/reportprocessor/dcmtk-3.6.2-linux-x86_64/share/dcmtk/dicom.dic"