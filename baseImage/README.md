## Report processor base image

The base image for this service is built and stored in the phcrcacentral0.azurecr.io registry separately from the service image. 

It requires building several libraries from source in order to make some dependencies work, and takes A LONG TIME.
However, it should need to be updated less frequently, so storing this base will speed up dev cycles by reducing build and pipeline time.