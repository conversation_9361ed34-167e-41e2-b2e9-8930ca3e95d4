{"keyvault_name": "phtestkvcacentral0", "az_storage_account": "phdevcentralcabloblrs", "az_container": "prod", "insights_storage_account": "phdevcentralcabloblrs", "insights_container": "reportinsightsdata", "mysql_connection_string_sec_name": "rp-sql-conn-str", "allowed_origins": [], "service_bus_conn_str_sec_name": "rp-sb-conn-str", "topic_name": "transfers", "subscription": "RP-transfer_challenge", "poll_frequency_seconds": 5, "insights_client_url": "http://report-insights-service.default.svc.cluster.local:4000", "insights_basic_auth_pw": "ca-ri-basic-auth-qa-pw", "gateway_auth_url": "https://gatewayauth.global.qa.pocket.health"}