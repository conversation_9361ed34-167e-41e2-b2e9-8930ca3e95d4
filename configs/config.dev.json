{"keyvault_name": "phtestkvcacentral0", "az_storage_account": "phdevcentralcabloblrs", "az_container": "prod", "insights_storage_account": "phdevcentralcabloblrs", "insights_container": "reportinsightsdata", "mysql_connection_string": "devuser:devpw@tcp(apptest-mysql-1:3306)/reportprocessor?parseTime=true", "allowed_origins": [], "service_bus_conn_str_sec_name": "rp-sb-conn-str", "topic_name": "transfers", "subscription": "RP-transfer_challenge", "poll_frequency_seconds": 5, "insights_client_url": "http://host.docker.internal:4000", "insights_basic_auth_pw": "ca-ri-basic-auth-dev-pw", "gateway_auth_url": "https://gatewayauth.global.qa.pocket.health"}