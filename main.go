package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gorilla/handlers"
	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
	phconfig "gitlab.com/pockethealth/phutils/v10/pkg/config"
	"gitlab.com/pockethealth/phutils/v10/pkg/logutils"
	"gitlab.com/pockethealth/reportprocessor/pkg/auth"
	"gitlab.com/pockethealth/reportprocessor/pkg/definitions"
	"gitlab.com/pockethealth/reportprocessor/pkg/insights"
	"gitlab.com/pockethealth/reportprocessor/pkg/reports"
	"gitlab.com/pockethealth/reportprocessor/pkg/rpApi"
	"gitlab.com/pockethealth/reportprocessor/pkg/startup"
	"gitlab.com/pockethealth/reportprocessor/pkg/tagger"
)

func usageAndExit() {
	fmt.Println("usage: ./reportprocessor --env [dev | qa | prod]")
	os.Exit(1)
}

func main() {
	// CLI args
	args := os.Args[1:]
	if len(args) != 2 || args[0] != "--env" {
		usageAndExit()
	}

	// Set up dependencies and config
	cfgJson := phconfig.SelectConfig("config", args[1])
	cfg, deps := startup.Setup(cfgJson)

	setupTmpDirs()

	// Configure Servicers and HTTP Server
	router := createRouter(*deps)
	n := logutils.SetupNegroni("rp", false)
	n.UseHandler(router)
	handler := handlers.CORS(
		handlers.AllowedOrigins(cfg.AllowedOrigins),
		handlers.AllowedHeaders(cfg.AllowedHeaders),
		handlers.AllowedMethods(cfg.AllowedMethods),
		handlers.ExposedHeaders(cfg.ExposedHeaders),
		handlers.AllowCredentials())(n)

	httpserver := &http.Server{Addr: ":7443", Handler: handler, ReadHeaderTimeout: 5 * time.Second}
	httpserver.ConnState = func(c net.Conn, cs http.ConnState) {
		var connErr error
		switch cs {
		case http.StateIdle, http.StateNew:
			connErr = c.SetReadDeadline(time.Now().Add(5 * time.Second))
		case http.StateActive:
			connErr = c.SetReadDeadline(time.Now().Add(1 * time.Hour))
		}
		if connErr != nil {
			logrus.WithError(connErr).Error("error configuring conn state")
		}
	}

	// Start shutdown hook, topic subs, and HTTP Server
	done := make(chan struct{})
	go sigtermHook(httpserver, cfg.ShutdownTimeSeconds, done, deps)

	for _, s := range deps.TopicSuscriptions {
		s.Listen()
	}

	logrus.Info("Listening...")
	if err := httpserver.ListenAndServe(); err != http.ErrServerClosed {
		// ErrServerClosed is the normal result of the shutdown hook succeeding
		logrus.WithError(err).Fatal("Server exited abnormally")
	}

	// block until idle connections closed
	<-done
}

func createRouter(deps startup.Dependencies) *mux.Router {
	dt := buildDefinitionTagger(deps)
	a := auth.Authenticator{
		AuthStore: auth.NewSQLAuthStore(deps.MySQLDB),
		APIUsers:  make(map[string]auth.APIUser, 0),
	}

	defStore := definitions.NewMysqlDefStore(deps.MySQLDB)
	reportsApiService := reports.NewReportsApiService(
		deps.Blobstore,
		dt,
		defStore,
	)
	insightsApiService := insights.InsightsApiService{
		InsightsJobDeps: insights.InsightsJobDeps{
			ObjsBlob:     deps.Blobstore,
			InsightsBlob: deps.InsightsBlobstore,
			InsightsSvc:  deps.InsightsClient,
			DefStore:     defStore,
		},
	}
	ReportsApiController := reports.NewReportsApiController(
		reportsApiService,
		&insightsApiService,
		a,
	)

	router := rpApi.NewRouter(ReportsApiController)

	return router
}

func setupTmpDirs() {
	// create tmp directory for temporary files during file conversion
	if _, err := os.Stat("vault"); os.IsNotExist(err) {
		mkdirErr := os.Mkdir("vault", 0o700)
		if mkdirErr != nil {
			log.Fatalf("failed to set up temp dirs %v", mkdirErr)
		}
	}
	if _, err := os.Stat("vault/tmp"); os.IsNotExist(err) {
		mkdirErr := os.Mkdir("vault/tmp", 0o700)
		if mkdirErr != nil {
			log.Fatalf("failed to set up temp dirs %v", mkdirErr)
		}
	}
}

func buildDefinitionTagger(deps startup.Dependencies) *tagger.Tagger {
	// TODO: batch up if this gets very large
	rows, err := deps.MySQLDB.Query(`
		SELECT term, is_organviz_organ
		  FROM term_definitions
		 WHERE (def <> "" OR is_organviz_organ=1)
	`)
	if err != nil {
		log.Printf("could not load term definitions, no tagging can occur, err: %v", err)
		return nil
	}
	defer rows.Close()
	termList := make([]string, 0)
	organVizTerms := make([]string, 0)
	for rows.Next() {
		var t string
		var organViz bool
		err := rows.Scan(&t, &organViz)
		if err != nil {
			log.Printf("issue reading a term. error: %v", err)
			continue
		}
		termList = append(termList, t)
		if organViz {
			organVizTerms = append(organVizTerms, t)
		}
	}
	defTagger := tagger.Tagger{}
	defTagger.Build(termList, organVizTerms)
	return &defTagger
}

func sigtermHook(
	server *http.Server,
	shutdownTimeoutSeconds int,
	idleConnsClosed chan struct{},
	deps *startup.Dependencies,
) {
	podName := os.Getenv("K8S_POD_NAME")
	nodeName := os.Getenv("K8S_NODE_NAME")

	// block until SIGTERM received
	sigint := make(chan os.Signal, 1)
	signal.Notify(sigint, syscall.SIGTERM)
	<-sigint

	shutdownTimeout := time.Duration(shutdownTimeoutSeconds) * time.Second
	logrus.WithFields(logrus.Fields{
		"pod":        podName,
		"node":       nodeName,
		"timeout_ms": shutdownTimeout.Milliseconds(),
	}).Info("Got sigterm, starting shutdown")

	// set up a context that times out after some limit
	ctx, cancel := context.WithTimeout(context.Background(), shutdownTimeout)
	defer cancel()

	// close listeners, then block until in-flight requests complete, unless the context errors out
	t0 := time.Now()
	if err := server.Shutdown(ctx); err != nil {
		// we get here if context timed out or if there was some
		// error with shutting down the server
		logrus.WithFields(logrus.Fields{
			"pod":        podName,
			"node":       nodeName,
			"error":      err,
			"timeout_ms": shutdownTimeout.Milliseconds(),
		}).Error("Error shutting down")
	}

	for _, s := range deps.TopicSuscriptions {
		s.Close()
	}

	logrus.Info("subscriptions closed")

	logrus.WithFields(logrus.Fields{
		"pod":      podName,
		"node":     nodeName,
		"taken_ms": time.Now().Sub(t0).Milliseconds(),
	}).Info("Successful server shutdown")

	// signal that connections have closed
	close(idleConnsClosed)
}
