spec:
  inputs:
    image-name:
      description: "The image name, excluding the ACR DNS name. eg \"providerservice\""
    image-placeholder:
      description: "The value that will be used in the Kustomize templates for the image."
      default: "__POCKETHEALTH_SERVER_IMAGE__"
    image-repository:
      description: "The repository to write the new image to."
      default: "phcrcacentral0.azurecr.io"
---

acr_push:
  tags:
    - k8s-infra-dm
  stage: upload
  variables:
    NAME: "$[[ inputs.image-repository ]]/$[[ inputs.image-name ]]"
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  dependencies:
    - build_dist
  needs:
    - build_dist
  script:
    - /kaniko/executor
      --skip-unused-stages
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --reproducible
      --single-snapshot
      --label GIT_COMMIT="${CI_COMMIT_SHA}"
      --label GIT_BRANCH="${CI_COMMIT_BRANCH}"
      --label GIT_TAG="${CI_COMMIT_TAG}"
      --label GITLAB_PIPELINE_ID="${CI_PIPELINE_ID}"
      --destination="$NAME:$CI_COMMIT_SHORT_SHA"
      --build-arg "CI_JOB_TOKEN=$CI_JOB_TOKEN"
      --tar-path=image.tar
      --digest-file image_digest
      --target rp
    - |
      cat << EOF > ./image_info
      - name: $[[ inputs.image-placeholder ]]
        digest: $(cat image_digest | grep -o 'sha256:[0-9a-f][^ ]*')
        newName: ${NAME}
      EOF
  artifacts:
    paths:
      - image.tar
      - image_digest
      - image_info

jfrog_review:
  tags:
    - k8s-infra-dm
  needs:
    - acr_push
  stage: upload
  variables:
    NAME: "$[[ inputs.image-repository ]]/$[[ inputs.image-name ]]"
  image:
    name: alpine:3.19
  before_script:
    - apk --update add jq bash curl gcompat
    # Download JFrog CLI
    -  curl -fL https://install-cli.jfrog.io | sh
    # configure artifactory server
    - jf config add ph-jfrog --url="https://phtest.jfrog.io/" --access-token=$JFROG_TOKEN
    - jf config show
  script:
    # a known vuln exists in the base image of ubuntu:trusty
    # this image is quite fragile and will need more attention to properly fix
    # hence the --fail=false
    - jf scan image.tar --watches backend-std-watch --fail=false
    # check if go dependencies scan failed during build job
    - if [ -f _go_deps_sec_failed ]; then echo "Failing build since go dependencies scan failed.  Check output of 'preimage' GitLab job for details."; exit 3; fi