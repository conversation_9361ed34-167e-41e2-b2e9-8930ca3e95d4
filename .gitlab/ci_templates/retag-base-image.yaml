
# This horrible little job retags the base image as generated by the push-to-acr job, giving it the
# 1.0.0-trusty tag. All MRs that modify the base image push to this tag, which is Not Good ™️
# @todo Eliminate this job!
# One option is to add a build-time ARG to the Dockerfile FROM. This would need support in both CI
# and local builds, which is why I have not yet addressed it. This repo has killed me enough today
retag-base-image:
  stage: baseimage
  tags:
    - k8s-infra-dm
  variables:
    BASEIMG: phcrcacentral0.azurecr.io/reportprocessorbase
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  dependencies:
    - push-to-acr-reportprocessorbase
  needs:
    - push-to-acr-reportprocessorbase
  script:
    - echo "FROM ${BASEIMG}@$(cat reportprocessorbase.digest)"
      /kaniko/executor
      --dockerfile /dev/stdin
      --destination ${BASEIMG}:1.0.0-trusty
